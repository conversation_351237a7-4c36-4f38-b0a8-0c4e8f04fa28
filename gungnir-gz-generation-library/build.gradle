plugins {
    id 'java'
}

group 'com.gungnir'
version '0.0.1'


repositories {
    mavenCentral()
}

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.2'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.2'

    implementation 'org.springframework.boot:spring-boot-starter-validation:2.4.5'
//    implementation 'com.baomidou.mybatisplus.annotation:3.1.1'
//    com.baomidou.mybatisplus.annotation;
//    implementation 'com.baomidou:mybatis-plus-annotation:3.4.1'
//    implementation 'com.baomidou:mybatis-plus-annotation:3.4.1'
    implementation 'com.mybatis-flex:mybatis-flex-core:1.8.3'
}

test {
    useJUnitPlatform()
}