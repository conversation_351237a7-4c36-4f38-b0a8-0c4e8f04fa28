package com.gungnir.generation;

import com.gungnir.generation.dto.VideoTemplateDTO;
import com.tyrfing.model.Pagination;
import java.util.List;
import java.util.Map;

/**
 * 视频模板服务接口
 */
public interface VideoTemplateService {

    /**
     * @api {post} /video_template_service/query_all 查询所有视频模板
     * @apiName queryAll
     * @apiGroup videoTemplate
     * @apiDescription 查询所有视频模板
     * @apiParamExample {json} Request
     * {}
     * @apiSuccess {List} data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     {
     *       "id": "1",
     *       "videoUrl": "https://example.com/video.mp4",
     *       "title": "数学教学视频",
     *       "avatar": "https://example.com/avatar.jpg",
     *       "description": "视频描述",
     *       "teacherName": "张老师",
     *       "field": "数学",
     *       "sort": 1,
     *       "status": 1,
     *       "insertTime": "2023-05-15 08:19:50",
     *       "updateTime": "2023-05-15 08:19:50"
     *     }
     *   ]
     * }
     */
    List<Map<String, Object>> queryAll();

    /**
     * @api {post} /video_template_service/query_by_id 根据ID查询视频模板
     * @apiName queryById
     * @apiGroup videoTemplate
     * @apiDescription 根据ID查询视频模板
     * @apiParam {String} id 视频模板ID
     * @apiParamExample {json} Request
     * {
     *   "id": "1"
     * }
     * @apiSuccess {Map} data 视频模板
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "id": "1",
     *     "videoUrl": "https://example.com/video.mp4",
     *     "title": "数学教学视频",
     *     "avatar": "https://example.com/avatar.jpg",
     *     "description": "视频描述",
     *     "teacherName": "张老师",
     *     "field": "数学",
     *     "sort": 1,
     *     "status": 1,
     *     "insertTime": "2023-05-15 08:19:50",
     *     "updateTime": "2023-05-15 08:19:50"
     *   }
     * }
     */
    Map<String, Object> queryById(String id);

    /**
     * @api {post} /video_template_service/query_by_status 根据状态查询视频模板
     * @apiName queryByStatus
     * @apiGroup videoTemplate
     * @apiDescription 根据状态查询视频模板
     * @apiParam {Integer} status 状态
     * @apiParamExample {json} Request
     * {
     *   "status": 1
     * }
     * @apiSuccess {List} data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [...]
     * }
     */
    List<Map<String, Object>> queryByStatus(Integer status);

    /**
     * @api {post} /video_template_service/query_by_title_like 根据标题模糊查询视频模板
     * @apiName queryByTitleLike
     * @apiGroup videoTemplate
     * @apiDescription 根据标题模糊查询视频模板
     * @apiParam {String} keyword 关键词
     * @apiParamExample {json} Request
     * {
     *   "keyword": "数学"
     * }
     * @apiSuccess {List} data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [...]
     * }
     */
    List<Map<String, Object>> queryByTitleLike(String keyword);

    /**
     * @api {post} /video_template_service/query_by_teacher_name 根据老师名称查询视频模板
     * @apiName queryByTeacherName
     * @apiGroup videoTemplate
     * @apiDescription 根据老师名称查询视频模板
     * @apiParam {String} teacherName 老师名称
     * @apiParamExample {json} Request
     * {
     *   "teacherName": "张老师"
     * }
     * @apiSuccess {List} data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [...]
     * }
     */
    List<Map<String, Object>> queryByTeacherName(String teacherName);

    /**
     * @api {post} /video_template_service/query_by_field 根据领域查询视频模板
     * @apiName queryByField
     * @apiGroup videoTemplate
     * @apiDescription 根据领域查询视频模板
     * @apiParam {String} field 领域
     * @apiParamExample {json} Request
     * {
     *   "field": "数学"
     * }
     * @apiSuccess {List} data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [...]
     * }
     */
    List<Map<String, Object>> queryByField(String field);

    /**
     * @api {post} /video_template_service/query_recent_added 查询最近添加的视频模板
     * @apiName queryRecentAdded
     * @apiGroup videoTemplate
     * @apiDescription 查询最近添加的视频模板
     * @apiParam {Integer} limit 限制数量
     * @apiParamExample {json} Request
     * {
     *   "limit": 10
     * }
     * @apiSuccess {List} data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [...]
     * }
     */
    List<Map<String, Object>> queryRecentAdded(Integer limit);

    /**
     * @api {post} /video_template_service/add 添加视频模板
     * @apiName add
     * @apiGroup videoTemplate
     * @apiDescription 添加视频模板
     * @apiParam {String} id 视频模板ID
     * @apiParam {String} videoUrl 视频URL
     * @apiParam {String} [title] 标题
     * @apiParam {String} [avatar] 头像
     * @apiParam {String} [description] 描述
     * @apiParam {String} [teacherName] 老师姓名
     * @apiParam {String} [field] 领域
     * @apiParam {Integer} [sort] 排序
     * @apiParam {Integer} [status] 状态
     * @apiParamExample {json} Request
     * {
     *   "id": "1",
     *   "videoUrl": "https://example.com/video.mp4",
     *   "title": "数学教学视频",
     *   "avatar": "https://example.com/avatar.jpg",
     *   "description": "视频描述",
     *   "teacherName": "张老师",
     *   "field": "数学",
     *   "sort": 1,
     *   "status": 1
     * }
     * @apiSuccess {Boolean} data 添加结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean add(Map<String, Object> params);

    /**
     * @api {post} /video_template_service/update 更新视频模板
     * @apiName update
     * @apiGroup videoTemplate
     * @apiDescription 更新视频模板
     * @apiParam {String} id 视频模板ID
     * @apiParam {String} [videoUrl] 视频URL
     * @apiParam {String} [title] 标题
     * @apiParam {String} [avatar] 头像
     * @apiParam {String} [description] 描述
     * @apiParam {String} [teacherName] 老师姓名
     * @apiParam {String} [field] 领域
     * @apiParam {Integer} [sort] 排序
     * @apiParam {Integer} [status] 状态
     * @apiParamExample {json} Request
     * {
     *   "id": "1",
     *   "title": "更新后的数学教学视频"
     * }
     * @apiSuccess {Boolean} data 更新结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean update(Map<String, Object> params);

    /**
     * @api {post} /video_template_service/update_status 更新视频模板状态
     * @apiName updateStatus
     * @apiGroup videoTemplate
     * @apiDescription 更新视频模板状态
     * @apiParam {String} id 视频模板ID
     * @apiParam {Integer} status 状态
     * @apiParamExample {json} Request
     * {
     *   "id": "1",
     *   "status": 0
     * }
     * @apiSuccess {Boolean} data 更新结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean updateStatus(String id, Integer status);

    /**
     * @api {post} /video_template_service/delete 删除视频模板
     * @apiName delete
     * @apiGroup videoTemplate
     * @apiDescription 删除视频模板
     * @apiParam {String} id 视频模板ID
     * @apiParamExample {json} Request
     * {
     *   "id": "1"
     * }
     * @apiSuccess {Boolean} data 删除结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean delete(String id);

    /**
     * @api {post} /video_template_service/query_page 分页查询视频模板
     * @apiName queryPage
     * @apiGroup videoTemplate
     * @apiDescription 分页查询视频模板
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} [title] 标题(模糊查询)
     * @apiParam {String} [teacherName] 老师姓名
     * @apiParam {String} [field] 负责领域
     * @apiParam {Integer} [status] 状态
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "title": "数学",
     *   "status": 1
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 100,
     *     "data": [
     *       {
     *         "id": 1,
     *         "videoUrl": "https://example.com/video.mp4",
     *         "title": "数学教学视频",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "description": "视频描述",
     *         "teacherName": "张老师",
     *         "field": "数学",
     *         "sort": 1,
     *         "status": 1,
     *         "insertTime": "2023-05-15 08:19:50",
     *         "updateTime": "2023-05-15 08:19:50"
     *       }
     *     ]
     *   }
     * }
     */
    Pagination<VideoTemplateDTO> queryPage(Integer pageNum, Integer pageSize, Map<String, Object> params);

    /**
     * @api {post} /video_template_service/query_by_status_page 分页查询视频模板（按状态）
     * @apiName queryByStatusPage
     * @apiGroup videoTemplate
     * @apiDescription 根据状态分页查询视频模板
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {Integer} status 状态
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "status": 1
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 50,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<VideoTemplateDTO> queryByStatusPage(Integer pageNum, Integer pageSize, Integer status);

    /**
     * @api {post} /video_template_service/query_by_title_like_page 分页查询视频模板（按标题模糊查询）
     * @apiName queryByTitleLikePage
     * @apiGroup videoTemplate
     * @apiDescription 根据标题模糊分页查询视频模板
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} keyword 关键词
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "keyword": "数学"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 30,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<VideoTemplateDTO> queryByTitleLikePage(Integer pageNum, Integer pageSize, String keyword);

    /**
     * @api {post} /video_template_service/query_by_teacher_name_page 分页查询视频模板（按老师名称）
     * @apiName queryByTeacherNamePage
     * @apiGroup videoTemplate
     * @apiDescription 根据老师名称分页查询视频模板
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} teacherName 老师名称
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "teacherName": "张老师"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 20,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<VideoTemplateDTO> queryByTeacherNamePage(Integer pageNum, Integer pageSize, String teacherName);

    /**
     * @api {post} /video_template_service/query_by_field_page 分页查询视频模板（按领域）
     * @apiName queryByFieldPage
     * @apiGroup videoTemplate
     * @apiDescription 根据领域分页查询视频模板
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} field 领域
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "field": "数学"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 25,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<VideoTemplateDTO> queryByFieldPage(Integer pageNum, Integer pageSize, String field);

    /**
     * @api {post} /video_template_service/query_all_page 分页查询所有视频模板
     * @apiName queryAllPage
     * @apiGroup videoTemplate
     * @apiDescription 分页查询所有视频模板
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data 视频模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 100,
     *     "data": [
     *       {
     *         "id": 1,
     *         "videoUrl": "https://example.com/video.mp4",
     *         "title": "数学教学视频",
     *         "avatar": "https://example.com/avatar.jpg",
     *         "description": "视频描述",
     *         "teacherName": "张老师",
     *         "field": "数学",
     *         "sort": 1,
     *         "status": 1,
     *         "insertTime": "2023-05-15 08:19:50",
     *         "updateTime": "2023-05-15 08:19:50"
     *       }
     *     ]
     *   }
     * }
     */
    Pagination<VideoTemplateDTO> queryAllPage(Integer pageNum, Integer pageSize);
} 