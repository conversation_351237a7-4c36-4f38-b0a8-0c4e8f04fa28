package com.gungnir.generation;

/**
 * 生成类服务
 * Created by AlanQuain on 2025/5/16 
 */
public interface GenerateService {


    /**
     * @api {post} /generate_service/creation_video 生成视频
     * @apiName creationVideo
     * @apiGroup generateService
     * @apiDescription 无
     * @apiParam {String} pptId 用户选择的ppt对应的id
     * @apiParam {Integer} voiceTemplateId 语音模版id
     * @apiParam {Integer} videoTemplateId 视频模版id
     * @apiParamExample {json} Request
     * {
     *   "pptId": 1,
     *   "voiceTemplateId": 2,
     *   "videoTemplateId": 1
     * }
     * @apiSuccess {Boolean} data
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean creationVideo(Integer pptId, Integer voiceTemplateId, Integer videoTemplateId);


}
