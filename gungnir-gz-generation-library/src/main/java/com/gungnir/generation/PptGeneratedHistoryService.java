package com.gungnir.generation;

import com.gungnir.generation.dto.PptGeneratedHistoryDTO;
import com.tyrfing.model.Pagination;
import java.util.List;
import java.util.Map;

/**
 * PPT生成历史服务接口
 */
public interface PptGeneratedHistoryService {

    /**
     * @api {post} /ppt_generated_history_service/query_all 查询所有PPT生成历史
     * @apiName queryAll
     * @apiGroup pptGeneratedHistory
     * @apiDescription 查询所有PPT生成历史（未删除的）
     * @apiParamExample {json} Request
     * {}
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryAll();

    /**
     * @api {post} /ppt_generated_history_service/query_by_id 根据ID查询PPT生成历史
     * @apiName queryById
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据ID查询PPT生成历史
     * @apiParam {Integer} id PPT生成历史ID
     * @apiParamExample {json} Request
     * {
     *   "id": 1
     * }
     * @apiSuccess {Map} data PPT生成历史
     * @apiSuccessExample {json} Response
     * {   "code": 200,   "msg": "success",   "data": {...} }
     */
    Map<String, Object> queryById(Integer id);

    /**
     * @api {post} /ppt_generated_history_service/query_by_user_id 根据用户ID查询PPT生成历史
     * @apiName queryByUserId
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据用户ID查询PPT生成历史
     * @apiParam {String} userId 用户ID
     * @apiParamExample {json} Request
     * {
     *   "userId": "user_123"
     * }
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByUserId(String userId);

    /**
     * @api {post} /ppt_generated_history_service/query_by_document_id 根据文档ID查询PPT生成历史
     * @apiName queryByDocumentId
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据文档ID查询PPT生成历史
     * @apiParam {String} documentId 文档ID
     * @apiParamExample {json} Request
     * {
     *   "documentId": "doc_123"
     * }
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByDocumentId(String documentId);

    /**
     * @api {post} /ppt_generated_history_service/query_by_course_id 根据课程ID查询PPT生成历史
     * @apiName queryByCourseId
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据课程ID查询PPT生成历史
     * @apiParam {String} courseId 课程ID
     * @apiParamExample {json} Request
     * {
     *   "courseId": "course_123"
     * }
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByCourseId(String courseId);

    /**
     * @api {post} /ppt_generated_history_service/query_by_ppt_status 根据PPT状态查询PPT生成历史
     * @apiName queryByPptStatus
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据PPT状态查询PPT生成历史
     * @apiParam {Integer} pptStatus PPT状态（1:正常, 0:有问题）
     * @apiParamExample {json} Request
     * {
     *   "pptStatus": 1
     * }
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByPptStatus(Integer pptStatus);

    /**
     * @api {post} /ppt_generated_history_service/query_recent_generated 查询最近生成的PPT
     * @apiName queryRecentGenerated
     * @apiGroup pptGeneratedHistory
     * @apiDescription 查询最近生成的PPT
     * @apiParam {Integer} limit 限制数量
     * @apiParamExample {json} Request
     * {
     *   "limit": 10
     * }
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryRecentGenerated(Integer limit);

    /**
     * @api {post} /ppt_generated_history_service/query_by_ppt_name_like 根据PPT名称模糊查询
     * @apiName queryByPptNameLike
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据PPT名称模糊查询
     * @apiParam {String} pptName PPT名称
     * @apiParamExample {json} Request
     * {
     *   "pptName": "数学"
     * }
     * @apiSuccess {List} data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByPptNameLike(String pptName);

    /**
     * @api {post} /ppt_generated_history_service/add 添加PPT生成历史
     * @apiName add
     * @apiGroup pptGeneratedHistory
     * @apiDescription 添加PPT生成历史
     * @apiParam {Map} params
     * @apiParamExample {json} Request
     * {
     *   "pptUrl": "https://example.com/ppt/123.pptx",
     *   "pptName": "数学课件",
     *   "pptAvatar": "https://example.com/images/cover.jpg",
     *   "pptSize": 1024000,
     *   "pptPageCount": 20,
     *   "userId": "user_123",
     *   "documentId": "doc_123",
     *   "pptStatus": 1,
     *   "aiModelUsed": "GPT-4",
     *   "generationParameters": "{\"theme\":\"blue\",\"style\":\"modern\"}"
     * }
     * @apiSuccess {Integer} data 新增记录的ID
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": 1
     * }
     */
    Integer add(Map<String, Object> params);

    /**
     * @api {post} /ppt_generated_history_service/update 更新PPT生成历史
     * @apiName update
     * @apiGroup pptGeneratedHistory
     * @apiDescription 更新PPT生成历史
     * @apiParam {Integer} id PPT生成历史ID
     * @apiParam {String} [pptUrl] PPT的URL
     * @apiParam {String} [pptName] PPT名称
     * @apiParam {String} [pptAvatar] PPT封面图片地址
     * @apiParam {Long} [pptSize] PPT大小(字节)
     * @apiParam {Integer} [pptPageCount] PPT页数
     * @apiParam {String} [userId] 用户ID
     * @apiParam {String} [documentId] 文档ID
     * @apiParam {String} [courseId] 课程ID
     * @apiParam {Integer} [pptStatus] PPT状态(1:正常,0:有问题)
     * @apiParam {String} [aiModelUsed] 使用模型名称/版本
     * @apiParam {String} [generationParameters] 生成参数(JSON格式)
     * @apiParamExample {json} Request
     * {
     *   "id": 1,
     *   "pptName": "更新后的数学课件",
     *   "pptStatus": 0
     * }
     * @apiSuccess {Boolean} data 更新结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean update(Map<String, Object> params);

    /**
     * @api {post} /ppt_generated_history_service/delete 删除PPT生成历史
     * @apiName delete
     * @apiGroup pptGeneratedHistory
     * @apiDescription 软删除PPT生成历史
     * @apiParam {Integer} id PPT生成历史ID
     * @apiParamExample {json} Request
     * {
     *   "id": 1
     * }
     * @apiSuccess {Boolean} data 删除结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean delete(Integer id);

    /**
     * @api {post} /ppt_generated_history_service/query_page 分页查询PPT生成历史
     * @apiName queryPage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 分页查询PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} [userId] 用户ID
     * @apiParam {String} [pptName] PPT名称（模糊匹配）
     * @apiParam {String} [documentId] 文档ID
     * @apiParam {String} [courseId] 课程ID
     * @apiParam {Integer} [pptStatus] PPT状态
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "userId": "user_123",
     *   "pptStatus": 1
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 100,
     *     "data": [
     *       {
     *         "id": 1,
     *         "pptUrl": "https://example.com/ppt/123.pptx",
     *         "pptName": "数学课件",
     *         "pptAvatar": "https://example.com/images/cover.jpg",
     *         "pptSize": 1024000,
     *         "pptPageCount": 20,
     *         "userId": "user_123",
     *         "documentId": "doc_123",
     *         "courseId": "course_123",
     *         "pptStatus": 1,
     *         "insertTime": "2023-05-15 08:19:50",
     *         "updateTime": "2023-05-15 08:19:50",
     *         "deleteStatus": false,
     *         "deleteTime": null,
     *         "aiModelUsed": "GPT-4",
     *         "generationParameters": "{\"theme\":\"blue\",\"style\":\"modern\"}"
     *       }
     *     ]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryPage(Integer pageNum, Integer pageSize, Map<String, Object> params);

    /**
     * @api {post} /ppt_generated_history_service/query_by_user_id_page 分页查询用户PPT生成历史
     * @apiName queryByUserIdPage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据用户ID分页查询PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} userId 用户ID
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "userId": "user_123"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 20,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryByUserIdPage(Integer pageNum, Integer pageSize, String userId);

    /**
     * @api {post} /ppt_generated_history_service/query_by_document_id_page 分页查询文档PPT生成历史
     * @apiName queryByDocumentIdPage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据文档ID分页查询PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} documentId 文档ID
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "documentId": "doc_123"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 15,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryByDocumentIdPage(Integer pageNum, Integer pageSize, String documentId);

    /**
     * @api {post} /ppt_generated_history_service/query_by_ppt_name_like_page 分页模糊查询PPT名称
     * @apiName queryByPptNameLikePage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据PPT名称模糊分页查询PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} pptName PPT名称关键字
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "pptName": "数学"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 25,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryByPptNameLikePage(Integer pageNum, Integer pageSize, String pptName);

    /**
     * @api {post} /ppt_generated_history_service/query_by_ppt_status_page 分页查询PPT状态
     * @apiName queryByPptStatusPage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据PPT状态分页查询PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {Integer} pptStatus PPT状态(1:正常,0:有问题)
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "pptStatus": 1
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 75,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryByPptStatusPage(Integer pageNum, Integer pageSize, Integer pptStatus);

    /**
     * @api {post} /ppt_generated_history_service/query_all_page 分页查询所有PPT生成历史
     * @apiName queryAllPage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 分页查询所有PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 100,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryAllPage(Integer pageNum, Integer pageSize);

    /**
     * @api {post} /ppt_generated_history_service/query_by_course_id_page 分页查询课程PPT生成历史
     * @apiName queryByCourseIdPage
     * @apiGroup pptGeneratedHistory
     * @apiDescription 根据课程ID分页查询PPT生成历史
     * @apiParam {Integer} pageNum 页码
     * @apiParam {Integer} pageSize 每页大小
     * @apiParam {String} courseId 课程ID
     * @apiParamExample {json} Request
     * {
     *   "pageNum": 1,
     *   "pageSize": 10,
     *   "courseId": "course_123"
     * }
     * @apiSuccess {Object} data 分页数据
     * @apiSuccess {Integer} data.pageNum 当前页码
     * @apiSuccess {Integer} data.pageSize 每页大小
     * @apiSuccess {Long} data.total 总记录数
     * @apiSuccess {List} data.data PPT生成历史列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": {
     *     "pageNum": 1,
     *     "pageSize": 10,
     *     "total": 15,
     *     "data": [...]
     *   }
     * }
     */
    Pagination<PptGeneratedHistoryDTO> queryByCourseIdPage(Integer pageNum, Integer pageSize, String courseId);
} 