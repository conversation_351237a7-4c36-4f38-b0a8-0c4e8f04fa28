package com.gungnir.generation;

import java.util.List;
import java.util.Map;

/**
 * 语音合成服务接口
 * <AUTHOR>
 */
public interface VoiceSynthesisService {


    /**
     * @api {post} /voice_synthesis_service/query_all 查询所有语音合成记录
     * @apiName queryAll
     * @apiGroup voiceSynthesis
     * @apiDescription 查询所有语音合成记录
     * @apiParamExample {json} Request
     * {}
     * @apiSuccess {List} data 语音合成记录列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [...]
     * }
     */
    List<Map<String, Object>>  queryAll();

    /**
     * @api {post} /voice_synthesis_service/query_by_id 根据ID查询语音合成记录
     * @apiName queryById
     * @apiGroup voiceSynthesis
     * @apiDescription 根据ID查询语音合成记录
     * @apiParam {String} synthesisId
     * @apiParamExample {json} Request
     * {
     *   "synthesisId": "syn_12345"
     * }
     * @apiSuccess {Map} data 语音合成记录
     * @apiSuccessExample {json} Response
     * {   "code": 200,   "msg": "success",   "data": {...} }
     */
    Map<String, Object> queryById(String synthesisId);

    /**
     * @api {post} /voice_synthesis_service/query_by_segment_id 根据文本段ID查询语音合成记录
     * @apiName queryBySegmentId
     * @apiGroup voiceSynthesis
     * @apiDescription 根据文本段ID查询语音合成记录
     * @apiParam {String} segmentId
     * @apiParamExample {json} Request
     * {
     *   "segmentId": "seg_12345"
     * }
     * @apiSuccess {List} data 语音合成记录列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryBySegmentId(String segmentId);

    /**
     * @api {post} /voice_synthesis_service/query_by_actor_id 根据发音人ID查询语音合成记录
     * @apiName queryByActorId
     * @apiGroup voiceSynthesis
     * @apiDescription 根据发音人ID查询语音合成记录
     * @apiParam {String} actorId
     * @apiParamExample {json} Request
     * {
     *   "actorId": "actor_12345"
     * }
     * @apiSuccess {List} data 语音合成记录列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByActorId(String actorId);

    /**
     * @api {post} /voice_synthesis_service/query_by_status 根据状态查询语音合成记录
     * @apiName queryByStatus
     * @apiGroup voiceSynthesis
     * @apiDescription 根据状态查询语音合成记录
     * @apiParam {String} status 状态
     * @apiParamExample {json} Request
     * {
     *   "status": "completed"
     * }
     * @apiSuccess {List} data 语音合成记录列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByStatus(String status);

    /**
     * @api {post} /voice_synthesis_service/query_by_segment_and_actor 根据文本段ID和发音人ID查询语音合成记录
     * @apiName queryBySegmentAndActor
     * @apiGroup voiceSynthesis
     * @apiDescription 根据文本段ID和发音人ID查询语音合成记录
     * @apiParam {String} segmentId
     * @apiParam {String} actorId
     * @apiParamExample {json} Request
     * {
     *   "segmentId": "seg_12345",
     *   "actorId": "actor_12345"
     * }
     * @apiSuccess {Map} data 语音合成记录
     * @apiSuccessExample {json} Response
     * {   "code": 200,   "msg": "success",   "data": {...} }
     */
    Map<String, Object> queryBySegmentAndActor(String segmentId, String actorId);

    /**
     * @api {post} /voice_synthesis_service/query_recent_synthesis 查询最近合成的语音记录
     * @apiName queryRecentSynthesis
     * @apiGroup voiceSynthesis
     * @apiDescription 查询最近合成的语音记录
     * @apiParam {Integer} limit
     * @apiParamExample {json} Request
     * {
     *   "limit": 10
     * }
     * @apiSuccess {List} data 语音合成记录列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryRecentSynthesis(Integer limit);
} 