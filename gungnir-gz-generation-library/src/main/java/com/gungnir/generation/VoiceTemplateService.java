package com.gungnir.generation;

import java.util.List;
import java.util.Map;

/**
 * Created by AlanQuain on 2025/5/15
 */
public interface VoiceTemplateService {

    /**
     * @api {post} /voice_template_service/query_all 查询所有语音模板
     * @apiName queryAll
     * @apiGroup voiceTemplate
     * @apiDescription 查询所有语音模板
     * @apiParamExample {json} Request
     * {}
     * @apiSuccess {List} data 语音模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryAll();

    /**
     * @api {post} /voice_template_service/query_by_id 根据ID查询语音模板
     * @apiName queryById
     * @apiGroup voiceTemplate
     * @apiDescription 根据ID查询语音模板
     * @apiParam {Long} actorId 语音模板ID
     * @apiParamExample {json} Request
     * {
     *   "actorId": 1
     * }
     * @apiSuccess {Map} data 语音模板
     * @apiSuccessExample {json} Response
     * {   "code": 200,   "msg": "success",   "data": {...} }
     */
    Map<String, Object> queryById(Long actorId);

    /**
     * @api {post} /voice_template_service/query_by_name 根据名称查询语音模板
     * @apiName queryByName
     * @apiGroup voiceTemplate
     * @apiDescription 根据名称查询语音模板
     * @apiParam {String} actorName 语音模板名称
     * @apiParamExample {json} Request
     * {
     *   "actorName": "中年男声"
     * }
     * @apiSuccess {List} data 语音模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByName(String actorName);

    /**
     * @api {post} /voice_template_service/query_by_type 根据类型查询语音模板
     * @apiName queryByType
     * @apiGroup voiceTemplate
     * @apiDescription 根据类型查询语音模板
     * @apiParam {Integer} type 语音类型
     * @apiParamExample {json} Request
     * {
     *   "type": 1
     * }
     * @apiSuccess {List} data 语音模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByType(Integer type);

    /**
     * @api {post} /voice_template_service/query_by_language 根据语言查询语音模板
     * @apiName queryByLanguage
     * @apiGroup voiceTemplate
     * @apiDescription 根据语言查询语音模板
     * @apiParam {String} language 语言
     * @apiParamExample {json} Request
     * {
     *   "language": "zh-CN"
     * }
     * @apiSuccess {List} data 语音模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByLanguage(String language);

    /**
     * @api {post} /voice_template_service/query_by_status 根据状态查询语音模板
     * @apiName queryByStatus
     * @apiGroup voiceTemplate
     * @apiDescription 根据状态查询语音模板
     * @apiParam {Boolean} status 状态
     * @apiParamExample {json} Request
     * {
     *   "status": true
     * }
     * @apiSuccess {List} data 语音模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByStatus(Boolean status);

    /**
     * @api {post} /voice_template_service/query_by_condition 根据条件查询语音模板
     * @apiName queryByCondition
     * @apiGroup voiceTemplate
     * @apiDescription 根据条件查询语音模板
     * @apiParam {String} [language] 语言
     * @apiParam {Integer} [type] 类型
     * @apiParam {Boolean} [status] 状态
     * @apiParamExample {json} Request
     * {
     *   "language": "zh-CN",
     *   "type": 1,
     *   "status": true
     * }
     * @apiSuccess {List} data 语音模板列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByCondition(String language, Integer type, Boolean status);
}
