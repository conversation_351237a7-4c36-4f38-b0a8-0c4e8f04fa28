package com.gungnir.generation.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * PPT生成历史DTO
 */
public class PptGeneratedHistoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String pptUrl;
    private String pptName;
    private String pptAvatar;
    private Long pptSize;
    private Integer pptPageCount;
    private String userId;
    private String documentId;
    private String courseId;
    private Integer pptStatus;
    private String aiModelUsed;
    private String generationParameters;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPptUrl() {
        return pptUrl;
    }

    public void setPptUrl(String pptUrl) {
        this.pptUrl = pptUrl;
    }

    public String getPptName() {
        return pptName;
    }

    public void setPptName(String pptName) {
        this.pptName = pptName;
    }

    public String getPptAvatar() {
        return pptAvatar;
    }

    public void setPptAvatar(String pptAvatar) {
        this.pptAvatar = pptAvatar;
    }

    public Long getPptSize() {
        return pptSize;
    }

    public void setPptSize(Long pptSize) {
        this.pptSize = pptSize;
    }

    public Integer getPptPageCount() {
        return pptPageCount;
    }

    public void setPptPageCount(Integer pptPageCount) {
        this.pptPageCount = pptPageCount;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Integer getPptStatus() {
        return pptStatus;
    }

    public void setPptStatus(Integer pptStatus) {
        this.pptStatus = pptStatus;
    }


    public String getAiModelUsed() {
        return aiModelUsed;
    }

    public void setAiModelUsed(String aiModelUsed) {
        this.aiModelUsed = aiModelUsed;
    }

    public String getGenerationParameters() {
        return generationParameters;
    }

    public void setGenerationParameters(String generationParameters) {
        this.generationParameters = generationParameters;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }


} 