package com.gungnir.generation;

import java.util.List;
import java.util.Map;

/**
 * PPT讲稿服务接口
 */
public interface PptContentTextService {

    /**
     * @api {post} /ppt_content_text_service/query_all 查询所有PPT讲稿
     * @apiName queryAll
     * @apiGroup pptContentText
     * @apiDescription 查询所有PPT讲稿（未删除的）
     * @apiParamExample {json} Request
     * {}
     * @apiSuccess {List} data PPT讲稿列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryAll();

    /**
     * @api {post} /ppt_content_text_service/query_by_id 根据ID查询PPT讲稿
     * @apiName queryById
     * @apiGroup pptContentText
     * @apiDescription 根据ID查询PPT讲稿
     * @apiParam {Integer} id PPT讲稿ID
     * @apiParamExample {json} Request
     * {
     *   "id": 1
     * }
     * @apiSuccess {Map} data PPT讲稿
     * @apiSuccessExample {json} Response
     * {   "code": 200,   "msg": "success",   "data": {...} }
     */
    Map<String, Object> queryById(Integer id);

    /**
     * @api {post} /ppt_content_text_service/query_by_ppt_id 根据PPT ID查询PPT讲稿
     * @apiName queryByPptId
     * @apiGroup pptContentText
     * @apiDescription 根据PPTID查询讲稿
     * @apiParam {Integer} pptId PPT ID
     * @apiParamExample {json} Request
     * {
     *   "pptId": 1
     * }
     * @apiSuccess {List} data PPT讲稿列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByPptId(Integer pptId);

    /**
     * @api {post} /ppt_content_text_service/query_by_user_id 根据用户ID查询PPT讲稿
     * @apiName queryByUserId
     * @apiGroup pptContentText
     * @apiDescription 根据用户ID查询讲稿
     * @apiParam {String} userId 用户ID
     * @apiParamExample {json} Request
     * {
     *   "userId": "user_123"
     * }
     * @apiSuccess {List} data PPT讲稿列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByUserId(String userId);

    /**
     * @api {post} /ppt_content_text_service/query_by_ppt_id_and_user_id 根据PPT ID和用户ID查询PPT讲稿
     * @apiName queryByPptIdAndUserId
     * @apiGroup pptContentText
     * @apiDescription 根据PPTID和用户ID查询讲稿
     * @apiParam {Integer} pptId PPT ID
     * @apiParam {String} userId 用户ID
     * @apiParamExample {json} Request
     * {
     *   "pptId": 1,
     *   "userId": "user_123"
     * }
     * @apiSuccess {List} data PPT讲稿列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByPptIdAndUserId(Integer pptId, String userId);

    /**
     * @api {post} /ppt_content_text_service/query_recent_inserted 查询最近插入的PPT讲稿
     * @apiName queryRecentInserted
     * @apiGroup pptContentText
     * @apiDescription 查询最近插入的讲稿
     * @apiParam {Integer} limit 限制数量
     * @apiParamExample {json} Request
     * {
     *   "limit": 10
     * }
     * @apiSuccess {List} data PPT讲稿列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryRecentInserted(Integer limit);

    /**
     * @api {post} /ppt_content_text_service/query_by_text_like 根据文本内容模糊查询PPT讲稿
     * @apiName queryByTextLike
     * @apiGroup pptContentText
     * @apiDescription 根据文本内容模糊查询讲稿
     * @apiParam {String} keyword 关键词
     * @apiParamExample {json} Request
     * {
     *   "keyword": "数学"
     * }
     * @apiSuccess {List} data PPT讲稿列表
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": [
     *     "..."
     *   ]
     * }
     */
    List<Map<String, Object>> queryByTextLike(String keyword);

    /**
     * @api {post} /ppt_content_text_service/add 添加PPT讲稿
     * @apiName add
     * @apiGroup pptContentText
     * @apiDescription 添加PPT讲稿
     * @apiParam {Integer} pptId PPT ID
     * @apiParam {String} userId 用户ID
     * @apiParam {String} [url] 封面图
     * @apiParam {String} [text] 文本
     * @apiParam {String} [editText] 编辑后的内容
     * @apiParam {Integer} [polishCount] 润色次数
     * @apiParamExample {json} Request
     * {
     *   "pptId": 1,
     *   "userId": "user_123",
     *   "url": "https://example.com/images/cover.jpg",
     *   "text": "这是原始讲稿内容",
     *   "editText": "这是编辑后的讲稿内容",
     *   "polishCount": 0
     * }
     * @apiSuccess {Integer} data 新增记录的ID
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": 1
     * }
     */
    Integer add(Map<String, Object> params);

    /**
     * @api {post} /ppt_content_text_service/update 更新PPT讲稿
     * @apiName update
     * @apiGroup pptContentText
     * @apiDescription 更新PPT讲稿
     * @apiParam {Integer} id PPT讲稿ID
     * @apiParam {Integer} [pptId] PPT ID
     * @apiParam {String} [userId] 用户ID
     * @apiParam {String} [url] 封面图
     * @apiParam {String} [text] 文本
     * @apiParam {String} [editText] 编辑后的内容
     * @apiParam {Integer} [polishCount] 润色次数
     * @apiParamExample {json} Request
     * {
     *   "id": 1,
     *   "editText": "这是更新后的讲稿内容"
     * }
     * @apiSuccess {Boolean} data 更新结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean update(Map<String, Object> params);

    /**
     * @api {post} /ppt_content_text_service/increment_polish_count 增加润色次数
     * @apiName incrementPolishCount
     * @apiGroup pptContentText
     * @apiDescription 增加讲稿的润色次数
     * @apiParam {Integer} id PPT讲稿ID
     * @apiParamExample {json} Request
     * {
     *   "id": 1
     * }
     * @apiSuccess {Boolean} data 操作结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean incrementPolishCount(Integer id);

    /**
     * @api {post} /ppt_content_text_service/delete 删除PPT讲稿
     * @apiName delete
     * @apiGroup pptContentText
     * @apiDescription 软删除PPT讲稿
     * @apiParam {Integer} id PPT讲稿ID
     * @apiParamExample {json} Request
     * {
     *   "id": 1
     * }
     * @apiSuccess {Boolean} data 删除结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean delete(Integer id);

    /**
     * @api {post} /ppt_content_text_service/delete_by_ppt_id 根据PPT ID删除所有讲稿
     * @apiName deleteByPptId
     * @apiGroup pptContentText
     * @apiDescription 根据PPTID软删除所有相关讲稿
     * @apiParam {Integer} pptId PPT ID
     * @apiParamExample {json} Request
     * {
     *   "pptId": 1
     * }
     * @apiSuccess {Boolean} data 删除结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean deleteByPptId(Integer pptId);

    /**
     * @api {post} /ppt_content_text_service/update_edit_text updateEditText
     * @apiName updateEditText
     * @apiGroup pptContentText
     * @apiDescription 更新讲稿的编辑文本并增加润色次数
     * @apiParam {Integer} id PPT讲稿ID
     * @apiParam {String} text 新的编辑文本
     * @apiParamExample {json} Request
     * {
     *   "id": 1,
     *   "text": "这是润色后的新内容"
     * }
     * @apiSuccess {Boolean} data 操作结果
     * @apiSuccessExample {json} Response
     * {
     *   "code": 200,
     *   "msg": "success",
     *   "data": true
     * }
     */
    Boolean updateEditText(Integer id, String text);
} 