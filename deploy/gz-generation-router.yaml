apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: gz-generation-ingressroute
  namespace: gungnir
spec:
  entryPoints:
    - web
  routes:
    - match: Host(`test.canghaiguanzhi.com`, `dxteacher.canghaiguanzhi.com`) && PathPrefix(`/dapi/v2/gz-generation`)
      kind: Rule
      services:
        - name: gz-generation
          port: 8080

      middlewares:
        - name: gungnir-compress
        - name: system-code-gungnir
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: gz-generation-https-ingressroute
  namespace: gungnir
spec:
  entryPoints:
    - websecure
  routes:
    - match: Host(`test.canghaiguanzhi.com`, `dxteacher.canghaiguanzhi.com`) && PathPrefix(`/dapi/v2/gz-generation`)
      kind: Rule
      priority: 99
      services:
        - name: gz-generation
          port: 8080

      middlewares:
        - name: gungnir-compress
        - name: system-code-gungnir
  tls:
    secretName: devsecret
