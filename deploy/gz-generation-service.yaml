apiVersion: v1
kind: Service
metadata:
  namespace: gungnir
  name: gz-generation
  labels:
    app: gz-generation
spec:
  type: ClusterIP
  ports:
    - name: web
      port: 8080
      protocol: TCP
      targetPort: 8080
    - name: api
      port: 10050
      protocol: TCP
      targetPort: 10050
  selector:
    app: gz-generation
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: gz-generation
    com.gungnir/component: svc
    k8s.eip.work/name: svc-gz-generation
  name: gz-generation
  namespace: gungnir
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gz-generation
  template:
    metadata:
      labels:
        app: gz-generation
    spec:
      containers:
        - env:
            - name: DUBBO_IP_TO_REGISTRY
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: DUBBO_PORT
              value: '10050'
            - name: JAEGER_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: MONGO_CONFIG_NAME
              value: mongodb-gungnir
          envFrom:
            - configMapRef:
                name: backend-common-configmap
          image: 'registry.cn-hangzhou.aliyuncs.com/chgz/gungnir-gz-generation:1d29557b'
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 60
            httpGet:
              path: /dapi/v2/gz-generation/actuator/health/liveness
              port: web
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          name: gz-generation
          ports:
            - containerPort: 8080
              name: web
              protocol: TCP
            - containerPort: 10050
              name: api
              protocol: TCP
          readinessProbe:
            failureThreshold: 60
            httpGet:
              path: /dapi/v2/gz-generation/actuator/health/liveness
              port: web
              scheme: HTTP
            initialDelaySeconds: 15
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
      imagePullSecrets:
        - name: aliyun-canghaiguanzhi
      restartPolicy: Always
---

