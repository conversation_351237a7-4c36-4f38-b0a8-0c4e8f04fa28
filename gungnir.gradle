ext {
    tyrfingVersion = '2.0.6-SNAPSHOT'
}

if (file("$rootDir/.git").exists()) {
    copy {
        from "$rootDir/hooks"
        into "$rootDir/.git/hooks"
        fileMode 0777
    }
}

if (file("$rootDir/conf/ignore.conf").exists()) {
    delete "$rootDir/.gitignore";
    copy {
        from "$rootDir/conf"
        into "$rootDir"
        fileMode 0777
        rename("ignore.conf", ".gitignore")
    }
    delete "$rootDir/conf"
}

subprojects {
    apply plugin: 'java'
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/public'
        }

          maven {
      

             credentials {
                username '649bc6d6d8d1dc71ed1193d6'
                password 'IkQCa4wv)vTk'
            }
                url 'https://packages.aliyun.com/674fc69f2875033c47c49030/maven/chgz-snapshot'
        }

        maven {
              credentials {
                  username = '674fc644ddf7780be096df47'
                  password = ']gUfV7Q[7)F['
                  }
               url 'https://packages.aliyun.com/674fc69f2875033c47c49030/maven/chgz-release'
         
        }


        // maven {
        //     credentials {
        //         username '5ed4b60a723771c6a541f88d'
        //         password 'm-NCqYTGv-tj'
        //     }
        //     url 'https://packages.aliyun.com/maven/repository/2429534-release-t6pGZk/'
        // }

        // maven {
        //     credentials {
        //         username '5ed4b60a723771c6a541f88d'
        //         password 'm-NCqYTGv-tj'
        //     }
        //     url 'https://packages.aliyun.com/maven/repository/2429534-snapshot-WgWeTL/'
        // }
    }

    configurations.all {
        // check for updates every build
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }

    dependencies {
        compile "com.tyrfing:tyrfing-spring-boot-starter:$tyrfingVersion"
    }

    compileJava {
        options.compilerArgs << '-parameters'
    }

    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }

    if (project.name.endsWith("-library")) {
        apply plugin: 'maven'


        def privateRepoUrl = 'https://packages.aliyun.com/674fc69f2875033c47c49030/maven/chgz-release/'

        uploadArchives {
            configuration = configurations.archives
            repositories {
                mavenDeployer {
                    repository(url: privateRepoUrl) {
                           
                        // authentication(userName: '5ed4b60a723771c6a541f88d', password: 'm-NCqYTGv-tj')
                         authentication(userName: '674fc644ddf7780be096df47', password: ']gUfV7Q[7)F[')
                    }

                    pom.project {
                        artifactId project.name
                        groupId 'com.gungnir'
                        packaging 'jar'
                        description project.description
                    }
                }
            }

            task tyrfingApi(type: Jar) {
                classifier = 'sources'
            }

            artifacts {
                archives tyrfingApi
            }
        }

        // def privateRepoUrl = 'https://packages.aliyun.com/maven/repository/2429534-release-t6pGZk/'

        // uploadArchives {
        //     configuration = configurations.archives
        //     repositories {
        //         mavenDeployer {
        //             repository(url: privateRepoUrl) {
        //                 authentication(userName: '5ed4b60a723771c6a541f88d', password: 'm-NCqYTGv-tj')
        //             }

        //             pom.project {
        //                 artifactId project.name
        //                 groupId 'com.gungnir'
        //                 packaging 'jar'
        //                 description project.description
        //             }
        //         }
        //     }

        //     task tyrfingApi(type: Jar) {
        //         classifier = 'sources'
        //     }

        //     artifacts {
        //         archives tyrfingApi
        //     }
        // }
    }
    if (project.name.endsWith("-application")) {
        apply plugin: 'java'
        apply plugin: 'com.google.cloud.tools.jib'

        dependencies {
            compile project(":${project.name.replace("-application", "-library")}")
        }

        jib {
            from.image = 'baxterchen/openjdk:8u181-jre-alpine-Shanghai'
            to {
                // image = 'registry.cn-hangzhou.aliyuncs.com/gungnir/' + project.name.replace('-application', '')
                image = 'registry.cn-hangzhou.aliyuncs.com/chgz/' + project.name.replace('-application', '')
                tags = [version]
            }
            container {
                ports = ['8080', '10050']
                labels = [
                        "traefik.frontend.rule": "PathPrefix:/dapi/v2/${project.name.split('-')[1]}".toString(),
                        "traefik.port"         : "8080"
                ]
                environment = [
                        "SPRING_APPLICATION_NAME": project.name.split('-')[1]
                ]
                args = [
                        "--spring.profiles.active=prod"
                ]
                useCurrentTimestamp = true
            }
        }
    }
}
