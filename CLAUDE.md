# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Java Spring Boot microservice called "gungnir-gz-generation" (合成视频合成语音服务 - Video and Voice Synthesis Service) built using Gradle. The project follows a multi-module architecture with two main modules:

- **gungnir-gz-generation-application**: Main Spring Boot application containing controllers, services, configurations, and data mappers
- **gungnir-gz-generation-library**: Library module containing service interfaces and DTOs

## Development Commands

### Build and Test
```bash
# Build the entire project
./gradlew build

# Run tests
./gradlew test

# Clean and build
./gradlew clean build
```

### Running the Application
```bash
# Run the application (defaults to port 8080)
./gradlew bootRun

# Run with production profile
./gradlew bootRun --args='--spring.profiles.active=prod'
```

### Docker Image Build
```bash
# Build Docker image using Jib
./gradlew jib
```

## Architecture

### Module Structure
- **Application Module**: Contains the main Spring Boot application, REST controllers, service implementations, and data access layers
- **Library Module**: Defines service interfaces and data transfer objects shared across the application

### Key Technologies
- Spring Boot 2.1.6
- MyBatis-Flex for database operations (MySQL and MongoDB)
- Dubbo for RPC communication
- Redis for caching
- Alibaba Druid for connection pooling
- Custom Tyrfing framework components

### Database Configuration
- **MySQL**: Primary database using dynamic datasource configuration
- **MongoDB**: Used for document storage and analysis
- **Redis**: Used for caching and session management

### Service Architecture
The application provides services for:
- PPT content text processing
- Video template management
- Voice synthesis
- File processing and manipulation

### Configuration Profiles
- Default profile uses local development settings
- Production profile (`prod`) uses environment variables for external service connections

### API Context Path
The application serves APIs under `/dapi/v2/gz-generation`

### External Dependencies
- Custom Gungnir libraries for office operations and account management
- Tyrfing framework components for database, Redis, file upload, and authentication
- Various third-party libraries for PDF processing, JSON handling, and HTTP operations