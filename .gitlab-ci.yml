stages:
  - apidoc
  - build
  - version
  - deploy

deploy_test:
  stage: deploy
  script: |
    curl -X PATCH \
      -H "content-type: application/strategic-merge-patch+json" \
      -H "Authorization:Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" \
      -d '{"spec":{"template":{"spec":{"containers":[{"name":"gz-generation","image":"registry.cn-hangzhou.aliyuncs.com/gungnir/gungnir-gz-generation:'$CI_COMMIT_SHORT_SHA'"}]}}}}' \
      "http://*************/k8s-api/apis/apps/v1/namespaces/gungnir/deployments/gz-generation"
  tags:
    - ssh
  only:
    refs:
      - main
    changes:
      - gungnir-gz-generation-library/**/*
      - gungnir-gz-generation-application/**/*
      - build.gradle
      - gungnir.gradle
      - .gitlab-ci.yml

create_apidoc:
  stage: apidoc
  script:
    - apidoc -i gungnir-gz-generation-library/ -o apidoc/gz-generation
    - cp -R -f apidoc/gz-generation/ /home/<USER>/gungnir-apidoc/pages
  tags:
    - apidoc
  only:
    refs:
      - main
    changes:
      - gungnir-gz-generation-library/**/*

push_release_image:
  stage: build
  script:
    - chmod +x gradlew
    - ./gradlew :gungnir-gz-generation-application:jib -Djib.to.image=registry.cn-hangzhou.aliyuncs.com/gungnir/gungnir-gz-generation -Djib.to.tags=$CI_COMMIT_REF_NAME,release -Djib.to.auth.username=<EMAIL> -Djib.to.auth.password=Asdf123456 -Djib.from.auth.username=<EMAIL> -Djib.from.auth.password=Asdf123456 -Djib.from.image=registry.cn-hangzhou.aliyuncs.com/tyrfing/openjdk:8u181-jre-alpine-Shanghai
  tags:
    - gradle
  only:
    - tags

push_dev_image:
  stage: build
  script:
    - chmod +x gradlew
    - ./gradlew :gungnir-gz-generation-application:jib -Djib.to.image=registry.cn-hangzhou.aliyuncs.com/gungnir/gungnir-gz-generation -Djib.to.tags=$CI_COMMIT_SHORT_SHA,dev -Djib.to.auth.username=<EMAIL> -Djib.to.auth.password=Asdf123456 -Djib.from.auth.username=<EMAIL> -Djib.from.auth.password=Asdf123456 -Djib.from.image=registry.cn-hangzhou.aliyuncs.com/tyrfing/openjdk:8u181-jre-alpine-Shanghai
  tags:
    - gradle
  only:
    refs:
      - main
    changes:
      - gungnir-gz-generation-library/**/*
      - gungnir-gz-generation-application/**/*
      - build.gradle
      - gungnir.gradle
      - .gitlab-ci.yml

push_producer_jar:
  stage: build
  script:
    - chmod +x gradlew
    - ./gradlew :gungnir-gz-generation-library:uploadArchives
  tags:
    - gradle
  only:
    refs:
      - main
    changes:
      - gungnir-gz-generation-library/build.gradle
  allow_failure: true
build_version_badge:
  stage: version
  script:
    - version.py
  tags:
    - gradle
  only:
    refs:
      - main
    changes:
      - gungnir-gz-generation-library/build.gradle
