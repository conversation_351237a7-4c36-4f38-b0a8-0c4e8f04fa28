plugins {
    id 'java'
    id 'com.google.cloud.tools.jib' version '1.3.0'
    id 'org.springframework.boot' version '2.1.6.RELEASE'
}

apply from: "$rootDir/gungnir.gradle"

group 'com.gungnir'
version '1.0.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.2'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.2'
}

test {
    useJUnitPlatform()
}