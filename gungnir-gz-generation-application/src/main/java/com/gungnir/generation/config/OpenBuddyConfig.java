package com.gungnir.generation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenBuddyConfig {

    /**
     * uri
     */
    @Value("${tyrfing.open-buddy.uri}")
    private String uri;

    /**
     * 凭证
     */
    @Value("${tyrfing.open-buddy.api-key}")
    private String apiKey;

    /**
     * 模型
     */
    @Value("${tyrfing.open-buddy.model}")
    private String model;

    /**
     * 控制AI生成结果的随机性的值，范围在0和0.9之间，值越大结果越具备随机性。
     */
    @Value("${tyrfing.open-buddy.temperature}")
    private Double temperature;

    /**
     * 单次响应AI生成的token数
     */
    @Value("${tyrfing.open-buddy.max-new-tokens}")
    private Integer maxNewTokens;

    /**
     * 单次响应AI生成的token数
     */
    @Value("${tyrfing.open-buddy.max-tokens}")
    private Integer maxTokens;

    /**
     * 机器翻译uri
     */
    @Value("${tyrfing.open-buddy.translate.uri}")
    private String translateUri;

    /**
     * 机器翻译凭证
     */
    @Value("${tyrfing.open-buddy.translate.token}")
    private String translateToken;

    /**
     * 机器翻译的模型
     */
    @Value("${tyrfing.open-buddy.translate.model}")
    private String translateModel;

    /**
     * 机器翻译的控制AI生成结果的随机性的值，范围在0和0.9之间，值越大结果越具备随机性。
     */
    @Value("${tyrfing.open-buddy.translate.temperature}")
    private Double translateTemperature;

    /**
     * 机器翻译的token数
     */
    @Value("${tyrfing.open-buddy.translate.max-new-tokens}")
    private Integer translateMaxNewTokens;

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }


    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Double getTemperature() {
        return temperature;
    }

    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }

    public Integer getMaxNewTokens() {
        return maxNewTokens;
    }

    public void setMaxNewTokens(Integer maxNewTokens) {
        this.maxNewTokens = maxNewTokens;
    }

    public String getTranslateModel() {
        return translateModel;
    }

    public void setTranslateModel(String translateModel) {
        this.translateModel = translateModel;
    }

    public Double getTranslateTemperature() {
        return translateTemperature;
    }

    public void setTranslateTemperature(Double translateTemperature) {
        this.translateTemperature = translateTemperature;
    }

    public Integer getTranslateMaxNewTokens() {
        return translateMaxNewTokens;
    }

    public void setTranslateMaxNewTokens(Integer translateMaxNewTokens) {
        this.translateMaxNewTokens = translateMaxNewTokens;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public String getTranslateUri() {
        return translateUri;
    }

    public void setTranslateUri(String translateUri) {
        this.translateUri = translateUri;
    }

    public String getTranslateToken() {
        return translateToken;
    }

    public void setTranslateToken(String translateToken) {
        this.translateToken = translateToken;
    }


    @Override
    public String toString() {
        return "OpenBuddyConfig{" +
                "uri='" + uri + '\'' +
                ", apiKey='" + apiKey + '\'' +
                ", model='" + model + '\'' +
                ", temperature=" + temperature +
                ", maxNewTokens=" + maxNewTokens +
                ", maxTokens=" + maxTokens +
                ", translateUri='" + translateUri + '\'' +
                ", translateToken='" + translateToken + '\'' +
                ", translateModel='" + translateModel + '\'' +
                ", translateTemperature=" + translateTemperature +
                ", translateMaxNewTokens=" + translateMaxNewTokens +
                '}';
    }
}
