package com.gungnir.generation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ResourceConfig {

    /**
     * oss资源PDF文件URI前缀
     */
    @Value("${tyrfing.oss-resource.pdf-uri-prefix}")
    private String pdfUriPrefix;

    public String getPdfUriPrefix() {
        return pdfUriPrefix;
    }

    public void setPdfUriPrefix(String pdfUriPrefix) {
        this.pdfUriPrefix = pdfUriPrefix;
    }

    @Override
    public String toString() {
        return "ResourceConfig{" +
                "pdfUriPrefix='" + pdfUriPrefix + '\'' +
                '}';
    }
}
