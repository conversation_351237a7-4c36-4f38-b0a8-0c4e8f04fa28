package com.gungnir.generation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/4/23
 * @title
 */
@Configuration
public class TextCheckConfig {

    @Value("${tyrfing.text-check.access-key}")
    private String accessKey;

    @Value("${tyrfing.text-check.access-key-secret}")
    private String accessKeySecret;

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    @Override
    public String toString() {
        return "TextCheckConfig{" +
                "accessKey='" + accessKey + '\'' +
                ", accessKeySecret='" + accessKeySecret + '\'' +
                '}';
    }
}
