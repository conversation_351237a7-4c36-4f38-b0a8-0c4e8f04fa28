package com.gungnir.generation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/8/23
 * @title
 */
@Configuration
public class RagConfig {

    /**
     * uri
     */
    @Value("${tyrfing.rag.uri}")
    private String uri;

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    @Override
    public String toString() {
        return "RagConfig{" +
                "uri='" + uri + '\'' +
                '}';
    }
}
