package com.gungnir.generation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SystemConfig {

    /**
     * 文档解析定时任务开关：0-关闭；1-开启
     */
    @Value("${tyrfing.job-switch.doc-analyze}")
    private Integer docAnalyzeJobSwitch;

    public Integer getDocAnalyzeJobSwitch() {
        return docAnalyzeJobSwitch;
    }

    public void setDocAnalyzeJobSwitch(Integer docAnalyzeJobSwitch) {
        this.docAnalyzeJobSwitch = docAnalyzeJobSwitch;
    }

    @Override
    public String toString() {
        return "SystemConfig{" +
                "docAnalyzeJobSwitch=" + docAnalyzeJobSwitch +
                '}';
    }
}
