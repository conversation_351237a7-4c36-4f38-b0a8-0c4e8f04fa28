package com.gungnir.generation.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

@Configuration
public class ApiAuthConfig implements Serializable {

    @Value("${tyrfing.api-auth.app-code}")
    private String appCode;

    @Value("${tyrfing.api-auth.access-token}")
    private String accessToken;

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    @Override
    public String toString() {
        return "ApiAuthConfig{" +
                "appCode='" + appCode + '\'' +
                ", accessToken='" + accessToken + '\'' +
                '}';
    }
}
