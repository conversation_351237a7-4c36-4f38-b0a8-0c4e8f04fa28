package com.gungnir.generation.mapper.mysql.common;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

public interface CommonExtQueryMapper<T> extends BaseMapper<T> {

    /**
     * 查询全部记录
     * <p color="red">仅当数据量较小时使用, 当业务数量规模和增长无法预估时, 请勿使用</p>
     *
     * @return List<T>
     */
    default List<T> selectAll() {
        return this.selectListByQuery(QueryWrapper.create());
    }

    /**
     * 根据 entity 条件，查询一条记录, 若where条件为空, 则返回null
     *
     * @param queryWrapper 实体对象封装操作类（entity 条件不可以为 null, 需标明）
     * @return T
     */
    default T selectOneByNotEmptyOfWhere(QueryWrapper queryWrapper) {
        if (queryWrapper.getWhere() == null || queryWrapper.getWhere().getSegments().isEmpty()) {
            return null;
        }
        return this.selectOneByQuery(queryWrapper);
    }

    /**
     * 根据 entity 条件，查询全部记录, 若where条件为空, 则返回空集
     *
     * @param queryWrapper 实体对象封装操作类（entity 条件不可以为 null, 需标明）
     * @return List<T>
     */
    default List<T> selectListByNotEmptyOfWhere(QueryWrapper queryWrapper) {
        if (queryWrapper.getWhere() == null || queryWrapper.getWhere().getSegments().isEmpty()) {
            return Collections.emptyList();
        }
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 指定1个字段作为查询的where条件
     */
    default <V> List<T> selectListBy(String column, V val) {
        return this.selectListByQuery(QueryWrapper.create().eq(column, val));
    }

    /**
     * 指定2个字段作为查询的where条件
     */
    default <V1, V2> List<T> selectListBy(String column1, V1 val1,
                                       String column2, V2 val2) {
        return this.selectListByQuery(
            QueryWrapper.create()
                .eq(column1, val1)
                .eq(column2, val2)
        );
    }

    /**
     * 指定3个字段作为查询的where条件
     */
    default <V1, V2, V3> List<T> selectListBy(String column1, V1 val1,
                                           String column2, V2 val2,
                                           String column3, V3 val3) {
        return this.selectListByQuery(
            QueryWrapper.create()
                .eq(column1, val1)
                .eq(column2, val2)
                .eq(column3, val3)
        );
    }

    /**
     * 指定1个字段作为查询的where条件
     */
    default <V> T selectOneBy(String column, V val) {
        return this.selectOneByQuery(QueryWrapper.create().eq(column, val));
    }

    /**
     * 指定2个字段作为查询的where条件
     */
    default <V1, V2> T selectOneBy(String column1, V1 val1,
                                String column2, V2 val2) {
        return this.selectOneByQuery(
            QueryWrapper.create()
                .eq(column1, val1)
                .eq(column2, val2)
        );
    }

    /**
     * 指定3个字段作为查询的where条件
     */
    default <V1, V2, V3> T selectOneBy(String column1, V1 val1,
                                    String column2, V2 val2,
                                    String column3, V3 val3) {
        return this.selectOneByQuery(
            QueryWrapper.create()
                .eq(column1, val1)
                .eq(column2, val2)
                .eq(column3, val3)
        );
    }

    /**
     * 指定1个字段作为查询的where条件
     */
    default <V> Optional<T> selectOneOptBy(String column, V val) {
        return Optional.ofNullable(selectOneBy(column, val));
    }

    /**
     * 指定2个字段作为查询的where条件
     */
    default <V1, V2> Optional<T> selectOneOptBy(String column1, V1 val1,
                                             String column2, V2 val2) {
        return Optional.ofNullable(selectOneBy(column1, val1, column2, val2));
    }

    /**
     * 指定3个字段作为查询的where条件
     */
    default <V1, V2, V3> Optional<T> selectOneOptBy(String column1, V1 val1,
                                                 String column2, V2 val2,
                                                 String column3, V3 val3) {
        return Optional.ofNullable(selectOneBy(column1, val1, column2, val2, column3, val3));
    }
}
