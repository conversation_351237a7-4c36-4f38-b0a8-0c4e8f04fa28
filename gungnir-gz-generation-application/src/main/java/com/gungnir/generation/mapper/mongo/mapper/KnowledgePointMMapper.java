package com.gungnir.generation.mapper.mongo.mapper;


import com.gungnir.generation.mapper.mongo.entity.KnowledgePointDto;
import com.gungnir.generation.util.CrudUtils;
import com.tyrfing.mongo.BaseMapper;
import com.tyrfing.mongo.TyrfingMongoDb;


public class KnowledgePointMMapper extends BaseMapper<KnowledgePointDto> {

    private final CrudUtils crudUtils;

    protected KnowledgePointMMapper(TyrfingMongoDb tyrfingMongoDb, CrudUtils crudUtils) {
        super(tyrfingMongoDb);
        this.crudUtils = crudUtils;
    }


}
