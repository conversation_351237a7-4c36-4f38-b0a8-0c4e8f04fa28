package com.gungnir.generation.mapper.mysql.table;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 语音合成表
 */
@Table(value = "voice_synthesis")
public class VoiceSynthesisTb {

    @Id
    private Long id;

    private Integer pptContentId;

    private Long voiceTemplateId;

    private String voiceUrl;

    private BigDecimal duration;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPptContentId() {
        return pptContentId;
    }

    public void setPptContentId(Integer pptContentId) {
        this.pptContentId = pptContentId;
    }

    public Long getVoiceTemplateId() {
        return voiceTemplateId;
    }

    public void setVoiceTemplateId(Long voiceTemplateId) {
        this.voiceTemplateId = voiceTemplateId;
    }

    public String getVoiceUrl() {
        return voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }

    public BigDecimal getDuration() {
        return duration;
    }

    public void setDuration(BigDecimal duration) {
        this.duration = duration;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "VoiceSynthesisTb{" +
                "id=" + id +
                ", pptContentId=" + pptContentId +
                ", voiceTemplateId=" + voiceTemplateId +
                ", voiceUrl='" + voiceUrl + '\'' +
                ", duration=" + duration +
                ", status='" + status + '\'' +
                ", insertTime=" + insertTime +
                ", updateTime=" + updateTime +
                '}';
    }
}