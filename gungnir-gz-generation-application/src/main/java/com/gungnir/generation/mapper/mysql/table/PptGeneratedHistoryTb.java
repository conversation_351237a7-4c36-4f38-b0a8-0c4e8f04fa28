package com.gungnir.generation.mapper.mysql.table;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * PPT生成历史表
 */
@Table(value = "ppt_generated_history")
public class PptGeneratedHistoryTb {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private String pptUrl;

    private String pptName;

    private String pptAvatar;

    private Long pptSize;

    private Integer pptPageCount;

    private String userId;

    private String documentId;

    private String courseId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private Integer pptStatus;

    private Boolean deleteStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;

    private String aiModelUsed;

    private String generationParameters;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPptUrl() {
        return pptUrl;
    }

    public void setPptUrl(String pptUrl) {
        this.pptUrl = pptUrl;
    }

    public String getPptName() {
        return pptName;
    }

    public void setPptName(String pptName) {
        this.pptName = pptName;
    }

    public String getPptAvatar() {
        return pptAvatar;
    }

    public void setPptAvatar(String pptAvatar) {
        this.pptAvatar = pptAvatar;
    }

    public Long getPptSize() {
        return pptSize;
    }

    public void setPptSize(Long pptSize) {
        this.pptSize = pptSize;
    }

    public Integer getPptPageCount() {
        return pptPageCount;
    }

    public void setPptPageCount(Integer pptPageCount) {
        this.pptPageCount = pptPageCount;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPptStatus() {
        return pptStatus;
    }

    public void setPptStatus(Integer pptStatus) {
        this.pptStatus = pptStatus;
    }

    public Boolean getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(Boolean deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getAiModelUsed() {
        return aiModelUsed;
    }

    public void setAiModelUsed(String aiModelUsed) {
        this.aiModelUsed = aiModelUsed;
    }

    public String getGenerationParameters() {
        return generationParameters;
    }

    public void setGenerationParameters(String generationParameters) {
        this.generationParameters = generationParameters;
    }

    @Override
    public String toString() {
        return "PptGeneratedHistoryTb{" +
                "id=" + id +
                ", pptUrl='" + pptUrl + '\'' +
                ", pptName='" + pptName + '\'' +
                ", pptAvatar='" + pptAvatar + '\'' +
                ", pptSize=" + pptSize +
                ", pptPageCount=" + pptPageCount +
                ", userId='" + userId + '\'' +
                ", documentId='" + documentId + '\'' +
                ", courseId='" + courseId + '\'' +
                ", insertTime=" + insertTime +
                ", updateTime=" + updateTime +
                ", pptStatus=" + pptStatus +
                ", deleteStatus=" + deleteStatus +
                ", deleteTime=" + deleteTime +
                ", aiModelUsed='" + aiModelUsed + '\'' +
                ", generationParameters='" + generationParameters + '\'' +
                '}';
    }
} 