package com.gungnir.generation.mapper.mongo.entity;

import java.io.Serializable;

public class AnalysisDocumentM implements Serializable {
    private String documentId;

    private String documentUrl;
    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getDocumentUrl() {
        return documentUrl;
    }

    public void setDocumentUrl(String documentUrl) {
        this.documentUrl = documentUrl;
    }

    @Override
    public String toString() {
        return "AnalysisDocumentM{" +
                "documentId='" + documentId + '\'' +
                ", documentUrl='" + documentUrl + '\'' +
                '}';
    }
}
