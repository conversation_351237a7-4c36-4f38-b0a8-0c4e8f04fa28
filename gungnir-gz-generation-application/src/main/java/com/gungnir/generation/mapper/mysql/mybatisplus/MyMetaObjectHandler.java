package com.gungnir.generation.mapper.mysql.mybatisplus;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 字段填充审计
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        //有创建时间字段，且字段值为空
        if (metaObject.hasGetter("insertTime")) {
            this.setInsertFieldValByName("insertTime", LocalDateTime.now(), metaObject);
        }
        //有值，则写入
        if (metaObject.hasGetter("deleted")) {
            this.setInsertFieldValByName("deleted", 0, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setUpdateFieldValByName("updateTime", LocalDateTime.now(), metaObject);
    }
}

