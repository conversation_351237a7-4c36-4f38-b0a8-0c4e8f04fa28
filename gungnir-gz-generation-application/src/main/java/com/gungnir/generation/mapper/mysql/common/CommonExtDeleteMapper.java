package com.gungnir.generation.mapper.mysql.common;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

public interface CommonExtDeleteMapper<T> extends BaseMapper<T> {

    /**
     * 根据1个字段作为where条件删除记录
     */
    default <V> int deleteBy(String column, V val) {
        return this.deleteByQuery(QueryWrapper.create().eq(column, val));
    }

    /**
     * 根据2个字段作为where条件删除记录
     */
    default <V1, V2> int deleteBy(String column1, V1 val1,
                               String column2, V2 val2) {
        return this.deleteByQuery(
            QueryWrapper.create()
                .eq(column1, val1)
                .eq(column2, val2));
    }
}
