package com.gungnir.generation.mapper.mysql.table;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR> 2025年05月15日09:48
 * 邮件: <EMAIL>
 * 作者: AlanQuain
 */
@Table(value = "voice_template")
public class VoiceTemplateTb {

    @Id(keyType = KeyType.Auto)
    private Long id;

    private String actorName;

    private String actorDescription;

    private String avatar;

    private String voiceType;

    private String language;

    private String exampleUrl;

    private Integer type;

    private String voiceApiCode;

    private Integer sort;

    private Boolean status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActorName() {
        return actorName;
    }

    public void setActorName(String actorName) {
        this.actorName = actorName;
    }

    public String getActorDescription() {
        return actorDescription;
    }

    public void setActorDescription(String actorDescription) {
        this.actorDescription = actorDescription;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getVoiceType() {
        return voiceType;
    }

    public void setVoiceType(String voiceType) {
        this.voiceType = voiceType;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getExampleUrl() {
        return exampleUrl;
    }

    public void setExampleUrl(String exampleUrl) {
        this.exampleUrl = exampleUrl;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getVoiceApiCode() {
        return voiceApiCode;
    }

    public void setVoiceApiCode(String voiceApiCode) {
        this.voiceApiCode = voiceApiCode;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "VoiceTemplateTb{" +
                "id=" + id +
                ", actorName='" + actorName + '\'' +
                ", actorDescription='" + actorDescription + '\'' +
                ", avatar='" + avatar + '\'' +
                ", voiceType='" + voiceType + '\'' +
                ", language='" + language + '\'' +
                ", exampleUrl='" + exampleUrl + '\'' +
                ", type=" + type +
                ", voiceApiCode='" + voiceApiCode + '\'' +
                ", sort=" + sort +
                ", status=" + status +
                ", insertTime=" + insertTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
