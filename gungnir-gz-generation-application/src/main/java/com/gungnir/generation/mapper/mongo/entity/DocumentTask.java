package com.gungnir.generation.mapper.mongo.entity;

import com.gungnir.generation.mapper.mysql.table.BaseEntity;

import java.util.Date;
import java.util.List;

public class DocumentTask extends BaseEntity{

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 开启时间 start_time
     * 算法更新
     */
    private Date startTime;



    /**
     * 主文档
     */
    private AnalysisDocumentM mainDocument;

    /**
     * 其它文档
     */
    private List<AnalysisDocumentM> otherDocuments;


    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }




    public AnalysisDocumentM getMainDocument() {
        return mainDocument;
    }

    public void setMainDocument(AnalysisDocumentM mainDocument) {
        this.mainDocument = mainDocument;
    }

    public List<AnalysisDocumentM> getOtherDocuments() {
        return otherDocuments;
    }

    public void setOtherDocuments(List<AnalysisDocumentM> otherDocuments) {
        this.otherDocuments = otherDocuments;
    }

    @Override
    public String toString() {
        return "CourseDocumentM{" +
                "courseId='" + courseId + '\'' +
                ", status='" + status + '\'' +
                ", startTime=" + startTime +
                ", mainDocument=" + mainDocument +
                ", otherDocuments=" + otherDocuments +
                '}';
    }
}
