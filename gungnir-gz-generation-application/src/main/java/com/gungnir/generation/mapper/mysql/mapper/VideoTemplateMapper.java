package com.gungnir.generation.mapper.mysql.mapper;

import com.gungnir.generation.mapper.mysql.common.CommonMapper;
import com.gungnir.generation.mapper.mysql.table.VideoTemplateTb;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 视频模板Mapper
 */
public interface VideoTemplateMapper extends CommonMapper<VideoTemplateTb> {

    /**
     * 根据状态查询视频模板
     *
     * @param status 状态
     * @return 视频模板列表
     */
    @Select("SELECT * FROM video_template WHERE status = #{status} ORDER BY sort ASC, insert_time DESC")
    List<VideoTemplateTb> selectByStatus(@Param("status") Integer status);

    /**
     * 根据标题模糊查询
     *
     * @param keyword 关键词
     * @return 视频模板列表
     */
    @Select("SELECT * FROM video_template WHERE title LIKE CONCAT('%', #{keyword}, '%') ORDER BY sort ASC, insert_time DESC")
    List<VideoTemplateTb> selectByTitleLike(@Param("keyword") String keyword);

    /**
     * 根据老师名称查询
     *
     * @param teacherName 老师名称
     * @return 视频模板列表
     */
    @Select("SELECT * FROM video_template WHERE teacher_name = #{teacherName} ORDER BY sort ASC, insert_time DESC")
    List<VideoTemplateTb> selectByTeacherName(@Param("teacherName") String teacherName);

    /**
     * 根据领域查询
     *
     * @param field 领域
     * @return 视频模板列表
     */
    @Select("SELECT * FROM video_template WHERE field = #{field} ORDER BY sort ASC, insert_time DESC")
    List<VideoTemplateTb> selectByField(@Param("field") String field);

    /**
     * 更新视频模板状态
     *
     * @param id 视频模板ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE video_template SET status = #{status}, update_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") String id, @Param("status") Integer status);

    /**
     * 查询最近添加的模板
     *
     * @param limit 限制数量
     * @return 视频模板列表
     */
    @Select("SELECT * FROM video_template ORDER BY insert_time DESC LIMIT #{limit}")
    List<VideoTemplateTb> selectRecentAdded(@Param("limit") Integer limit);
} 