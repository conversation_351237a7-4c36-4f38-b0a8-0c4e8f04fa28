package com.gungnir.generation.mapper.mysql.mapper;

import com.mybatisflex.core.query.QueryWrapper;
import com.gungnir.generation.mapper.mysql.common.CommonMapper;
import com.gungnir.generation.mapper.mysql.table.PptContentTextTb;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * PPT讲稿Mapper
 */
public interface PptContentTextMapper extends CommonMapper<PptContentTextTb> {

    /**
     * 根据PPT ID查询讲稿
     * @param pptId PPT ID
     * @return 讲稿列表
     */
    default List<PptContentTextTb> selectByPptId(Integer pptId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("ppt_id", pptId)
                .eq("delete_status", false)
                .orderBy("sort", true);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据用户ID查询讲稿
     * @param userId 用户ID
     * @return 讲稿列表
     */
    default List<PptContentTextTb> selectByUserId(String userId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("user_id", userId)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据PPT ID和用户ID查询讲稿
     * @param pptId PPT ID
     * @param userId 用户ID
     * @return 讲稿列表
     */
    default List<PptContentTextTb> selectByPptIdAndUserId(Integer pptId, String userId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("ppt_id", pptId)
                .eq("user_id", userId)
                .eq("delete_status", false)
                .orderBy("id", true);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 获取最近插入的讲稿
     * @param limit 限制数量
     * @return 讲稿列表
     */
    @Select("SELECT * FROM ppt_content_text WHERE delete_status = 0 ORDER BY insert_time DESC LIMIT #{limit}")
    List<PptContentTextTb> selectRecentInserted(@Param("limit") Integer limit);

    /**
     * 根据文本内容模糊查询
     * @param keyword 关键词
     * @return 讲稿列表
     */
    default List<PptContentTextTb> selectByTextLike(String keyword) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .like("text", keyword)
                .like("edit_text", keyword)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 增加润色次数
     * @param id 讲稿ID
     * @return 影响行数
     */
    @Update("UPDATE ppt_content_text SET polish_count = polish_count + 1, update_time = NOW() WHERE id = #{id} AND delete_status = 0")
    int incrementPolishCount(@Param("id") Integer id);

    /**
     * 根据PPT ID删除所有讲稿（软删除）
     * @param pptId PPT ID
     * @return 影响行数
     */
    @Update("UPDATE ppt_content_text SET delete_status = 1, delete_time = NOW() WHERE ppt_id = #{pptId} AND delete_status = 0")
    int deleteByPptId(@Param("pptId") Integer pptId);
} 