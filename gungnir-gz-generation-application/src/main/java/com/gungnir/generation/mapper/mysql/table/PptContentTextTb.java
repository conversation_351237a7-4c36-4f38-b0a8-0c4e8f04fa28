package com.gungnir.generation.mapper.mysql.table;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * PPT解析出来的讲稿
 */
@Table(value = "ppt_content_text")
public class PptContentTextTb {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private Integer pptId;

    private String userId;

    private String url;

    private String text;

    private String editText;

    private Integer polishCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date insertTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    private Boolean deleteStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPptId() {
        return pptId;
    }

    public void setPptId(Integer pptId) {
        this.pptId = pptId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getEditText() {
        return editText;
    }

    public void setEditText(String editText) {
        this.editText = editText;
    }

    public Integer getPolishCount() {
        return polishCount;
    }

    public void setPolishCount(Integer polishCount) {
        this.polishCount = polishCount;
    }

    public Date getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(Date insertTime) {
        this.insertTime = insertTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDeleteStatus() {
        return deleteStatus;
    }

    public void setDeleteStatus(Boolean deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public String toString() {
        return "PptContentTextTb{" +
                "id=" + id +
                ", pptId=" + pptId +
                ", userId='" + userId + "'" +
                ", url='" + url + "'" +
                ", text='" + text + "'" +
                ", editText='" + editText + "'" +
                ", polishCount=" + polishCount +
                ", insertTime=" + insertTime +
                ", updateTime=" + updateTime +
                ", deleteStatus=" + deleteStatus +
                ", deleteTime=" + deleteTime +
                '}';
    }
} 