package com.gungnir.generation.mapper.mongo.entity;

import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

@Document(collection = "knowledge_point")
public class KnowledgePointDto implements Serializable {

    private static final long serialVersionUID = 1L;



    public KnowledgePointDto(String documentId, String definition, String specificContent, String teachingSuggestions, String learningStatus, String knowledgePointName, String chapterId, String knowledgeTypeId, String learningStatusName, String title, String content, String order, String knowledgeLevel) {

        this.definition = definition;
        this.specificContent = specificContent;
        this.teachingSuggestions = teachingSuggestions;

        this.title = title;

    }


    public String getDefinition() {
        return definition;
    }

    public void setDefinition(String definition) {
        this.definition = definition;
    }

    public String getSpecificContent() {
        return specificContent;
    }

    public void setSpecificContent(String specificContent) {
        this.specificContent = specificContent;
    }

    public String getTeachingSuggestions() {
        return teachingSuggestions;
    }

    public void setTeachingSuggestions(String teachingSuggestions) {
        this.teachingSuggestions = teachingSuggestions;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    private String definition;

    private String specificContent;

    private String teachingSuggestions;


    private String title;


    @Override
    public String toString() {
        return "KnowledgePointDto{" +
                "definition='" + definition + '\'' +
                ", specificContent='" + specificContent + '\'' +
                ", teachingSuggestions='" + teachingSuggestions + '\'' +
                ", title='" + title + '\'' +
                '}';
    }

    public KnowledgePointDto() {
    }
}
