package com.gungnir.generation.mapper.mongo.mapper;

import com.gungnir.generation.mapper.mongo.entity.DocumentTask;
import com.gungnir.generation.mapper.mongo.entity.RunningTask;
import com.gungnir.generation.util.CrudUtils;
import com.tyrfing.exceptions.TyrfingServiceException;
import com.tyrfing.mongo.BaseMapper;
import com.tyrfing.mongo.TyrfingMongoDb;
import com.tyrfing.mongo.exception.TyrfingMongoException;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CourseDocuementMMapper extends BaseMapper<DocumentTask> {

    private final CrudUtils crudUtils;
    protected CourseDocuementMMapper(TyrfingMongoDb tyrfingMongoDb, CrudUtils crudUtils) {
        super(tyrfingMongoDb);
        this.crudUtils = crudUtils;
    }

    public void saveEntity(DocumentTask entity) throws TyrfingServiceException {
        crudUtils.save(entity);
    }

    public String getUpdateStatus(String courseId) throws TyrfingServiceException, TyrfingMongoException {
        MongoTemplate mongoTemplate = getMongoTemplate();
        return crudUtils.getUpdateStatus(courseId,mongoTemplate);
    }

    public List<RunningTask> getRunningTask() throws TyrfingMongoException {
        MongoTemplate mongoTemplate = getMongoTemplate();
        return crudUtils.getRunningTask("处理中",mongoTemplate);

    }

    public void updateTaskStatus(RunningTask runningTask) throws TyrfingMongoException {
        MongoTemplate mongoTemplate = getMongoTemplate();
         crudUtils.updateTaskStatus(runningTask,mongoTemplate);

    }
}
