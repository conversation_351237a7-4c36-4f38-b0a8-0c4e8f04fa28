package com.gungnir.generation.mapper.mysql.mapper;

import com.mybatisflex.core.query.QueryWrapper;
import com.gungnir.generation.mapper.mysql.common.CommonMapper;
import com.gungnir.generation.mapper.mysql.table.VoiceSynthesisTb;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 语音合成Mapper
 */
public interface VoiceSynthesisMapper extends CommonMapper<VoiceSynthesisTb> {

    /**
     * 根据segmentId查询语音合成记录
     * @param segmentId 文本段ID
     * @return 语音合成记录列表
     */
    default List<VoiceSynthesisTb> selectBySegmentId(String segmentId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("segment_id", segmentId);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据actorId查询语音合成记录
     * @param actorId 发音人ID
     * @return 语音合成记录列表
     */
    default List<VoiceSynthesisTb> selectByActorId(String actorId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("actor_id", actorId);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据状态查询语音合成记录
     * @param status 状态
     * @return 语音合成记录列表
     */
    default List<VoiceSynthesisTb> selectByStatus(String status) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("status", status);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据segmentId和actorId查询语音合成记录
     * @param segmentId 文本段ID
     * @param actorId 发音人ID
     * @return 语音合成记录
     */
    default VoiceSynthesisTb selectBySegmentAndActor(String segmentId, String actorId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("segment_id", segmentId)
                .eq("actor_id", actorId);
        return this.selectOneByQuery(queryWrapper);
    }

    /**
     * 获取最近合成的语音记录
     * @param limit 限制数量
     * @return 语音合成记录列表
     */
    @Select("SELECT * FROM voice_synthesis ORDER BY created_at DESC LIMIT #{limit}")
    List<VoiceSynthesisTb> selectRecentSynthesis(@Param("limit") Integer limit);
} 