package com.gungnir.generation.mapper.mysql.common;

import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateWrapper;

public interface CommonExtUpdateMapper<T> extends BaseMapper<T> {

    /**
     * 根据1个字段作为where条件更新记录
     */
    default <V> int updateBy(T entity, String column, V val) {
        return this.updateByQuery(entity, QueryWrapper.create().eq(column, val));
    }

    /**
     * 根据2个字段作为where条件更新记录
     */
    default <V1, V2> int updateBy(T entity,
                               String column1, V1 val1,
                               String column2, V2 val2) {
        return this.updateByQuery(entity,
            QueryWrapper.create()
                .eq(column1, val1)
                .eq(column2, val2));
    }
}
