package com.gungnir.generation.mapper.mysql.mapper;

import com.mybatisflex.core.query.QueryWrapper;
import com.gungnir.generation.mapper.mysql.common.CommonMapper;
import com.gungnir.generation.mapper.mysql.table.PptGeneratedHistoryTb;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * PPT生成历史Mapper
 */
public interface PptGeneratedHistoryMapper extends CommonMapper<PptGeneratedHistoryTb> {

    /**
     * 根据用户ID查询PPT生成历史
     * @param userId 用户ID
     * @return PPT生成历史列表
     */
    default List<PptGeneratedHistoryTb> selectByUserId(String userId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("user_id", userId)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据文档ID查询PPT生成历史
     * @param documentId 文档ID
     * @return PPT生成历史列表
     */
    default List<PptGeneratedHistoryTb> selectByDocumentId(String documentId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("document_id", documentId)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据课程ID查询PPT生成历史
     * @param courseId 课程ID
     * @return PPT生成历史列表
     */
    default List<PptGeneratedHistoryTb> selectByCourseId(String courseId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("course_id", courseId)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 根据PPT状态查询PPT生成历史
     * @param pptStatus PPT状态
     * @return PPT生成历史列表
     */
    default List<PptGeneratedHistoryTb> selectByPptStatus(Integer pptStatus) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("ppt_status", pptStatus)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }

    /**
     * 获取最近生成的PPT记录
     * @param limit 限制数量
     * @return PPT生成历史列表
     */
    @Select("SELECT * FROM ppt_generated_history WHERE delete_status = 0 ORDER BY insert_time DESC LIMIT #{limit}")
    List<PptGeneratedHistoryTb> selectRecentGenerated(@Param("limit") Integer limit);

    /**
     * 根据PPT名称模糊查询
     * @param pptName PPT名称
     * @return PPT生成历史列表
     */
    default List<PptGeneratedHistoryTb> selectByPptNameLike(String pptName) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .like("ppt_name", pptName)
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        return this.selectListByQuery(queryWrapper);
    }
} 