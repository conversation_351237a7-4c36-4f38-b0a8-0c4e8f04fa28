package com.gungnir.generation.util;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.tyrfing.common.TyrfingErrorCode;
import com.tyrfing.exceptions.TyrfingServiceException;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

public class OkHttpUtil {
    private static final Logger log = LoggerFactory.getLogger("OkHttpUtil");


    /**
     * 发送Post请求
     *
     * @param url  请求地址
     * @param body 请求体
     * @return 响应体
     * @throws TyrfingServiceException 请求异常
     */
    public static String post(String url, String body) throws TyrfingServiceException {
        String responseBodyStr;
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), body);
        Request request = new Request.Builder().url(url).post(reqBody).build();
        try {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            int time = 60;
            // 设置连接超时时间
            builder.connectTimeout(time, TimeUnit.SECONDS);
            // 设置读取超时时间
            builder.readTimeout(time, TimeUnit.SECONDS);
            // 设置写入超时时间
            builder.writeTimeout(time, TimeUnit.SECONDS);
            OkHttpClient client = builder.build();
            Response response = client.newCall(request).execute();
            responseBodyStr = response.body() != null ? response.body().string() : null;
            if (response.code() != HttpStatus.OK.value()) {
                log.error("post请求异常, Http状态码: {}, responseBody: {}, req body {}, url {}", response.code(), responseBodyStr,body,url);
                throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常");
            }
            response.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常 "+e.getMessage());
        }catch (Exception e){
            e.printStackTrace();
            throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常 "+e.getMessage());
        }
        return responseBodyStr;
    }


    /**
     * 请求RAG的流式请求
     *
     * @param request 请求
     * @return 流式响应
     */
    public static SseEmitter newStreamRequest(Request request) {
//        接收到 错误 重新stream 最多三次
        return streamRagRequest(request);
    }



    public static SseEmitter streamRagRequest(Request request) {
        SseEmitter sseEmitter = new SseEmitter();
        ExecutorService executor = Executors.newSingleThreadExecutor();
        String sessionId = IdWorker.getIdStr();
        log.info("streamRagRequest sessionId {}",sessionId);

//        openai chatgptapi 中断
        executor.execute(() -> {
//            String allRes="";
            OkHttpClient client = OkHttpUtil.getOkHttpClient();
            log.info("streamRagRequest 请求开始 sessionId [{}], url: {}", sessionId, request.url());
            Call call = client.newCall(request);
            try (Response response = call.execute()) {
                ResponseBody responseBody = response.body();
//                call.
                log.info("streamRagRequest 请求结束  sessionId [{}], url: {}, code: {}", sessionId, request.url(), response.code());
                if (response.isSuccessful() && responseBody != null) {
                    InputStream inputStream = responseBody.byteStream();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                        String line = "";
                        char[] buffer = new char[1024]; // 每次读取1024个字符
                        int numCharsRead;
                        while ((numCharsRead = reader.read(buffer)) != -1) {  // 读取字符到缓冲区
                            line = new String(buffer, 0, numCharsRead);

                            Map<String, Object> res = new HashMap();
                            String resString = "";
                            try {
                                String text = "";
                                text = line;
                                res.put("o", text);
//                                allRes+=text;
//                                if (allRes.contains("很抱歉不能回答")) {
//                                    call.cancel();
////                                    func 说错误 重新调用
//                                    return streamRagRequest(request);
//                                }

                                if (!res.isEmpty()) {
                                    resString = JsonUtil.toJsonString(res);
                                    sseEmitter.send(resString);
                                }
                            } catch (Exception e) {
                                log.error("流式响应IO异常终止: {}", e.getMessage());
                                e.printStackTrace();
                                break;
                            }

                        }
                    } finally {
                        sseEmitter.complete();

                    }
                } else {
                    String responseBodyStr="";
                    if (response.body() != null) {
                        responseBodyStr= response.body().string();
                        log.error("send request error, response: {}", responseBodyStr);
                    }
                    sseEmitter.completeWithError(new Exception("Request failed "+responseBodyStr));
                }
            } catch (Exception e) {
                e.printStackTrace();
                sseEmitter.completeWithError(new Exception("Request failed "+e.getMessage()));
            }
        });
        executor.shutdown();
        return sseEmitter;
    }



    public static SseEmitter streamRagRequest(Request request, int retryCount) {
        SseEmitter sseEmitter = new SseEmitter();
        ExecutorService executor = Executors.newSingleThreadExecutor();
        String sessionId = IdWorker.getIdStr();
        log.info("streamRagRequest sessionId {}", sessionId);

        executor.execute(() -> {
            OkHttpClient client = OkHttpUtil.getOkHttpClient();
            log.info("streamRagRequest 请求开始 sessionId [{}], url: {}", sessionId, request.url());
            Call call = client.newCall(request);
            try (Response response = call.execute()) {
                ResponseBody responseBody = response.body();
                log.info("streamRagRequest 请求结束  sessionId [{}], url: {}, code: {}", sessionId, request.url(), response.code());
                String  allStr="";
                if (response.isSuccessful() && responseBody != null) {
                    InputStream inputStream = responseBody.byteStream();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                        String line = "";
                        char[] buffer = new char[1024]; // 每次读取1024个字符
                        int numCharsRead;
                        while ((numCharsRead = reader.read(buffer)) != -1) {  // 读取字符到缓冲区
                            line = new String(buffer, 0, numCharsRead);

                            Map<String, Object> res = new HashMap<>();
                            String resString = "";
                            try {
                                String text = "";
                                text = line;
                                allStr+=text;
                                if(allStr.contains("不够明确哦")){
                                    retrySse(retryCount+1,sseEmitter,request);
                                    return;
                                }
                                res.put("o", text);
                                if (!res.isEmpty()) {
                                    resString = JsonUtil.toJsonString(res);
                                    sseEmitter.send(resString);
                                }
                            } catch (Exception e) {
                                log.error("流式响应IO异常终止: {}", e.getMessage());
                                e.printStackTrace();
                                break;
                            }

                        }
                    } finally {
                        sseEmitter.complete();
                    }
                } else {
                    String responseBodyStr = "";
                    if (response.body() != null) {
                        responseBodyStr = response.body().string();
                        log.error("send request error, response: {}", responseBodyStr);
                    }
                    if (retryCount < 3) { // 如果还有重试机会
                        log.info("尝试重新请求，当前重试次数: {}", retryCount + 1);
                        sseEmitter.completeWithError(new Exception("Request failed, retrying..."));
                        streamRagRequest(request, retryCount + 1); // 递归调用，增加重试次数
                    } else {
                        sseEmitter.completeWithError(new Exception("Request failed after 3 retries " + responseBodyStr));
                    }
                }
            } catch (Exception e) {
                log.error("Request failed with exception: {}", e.getMessage());
                if (retryCount < 3) { // 如果还有重试机会
                    log.info("尝试重新请求，当前重试次数: {}", retryCount + 1);
                    streamRagRequest(request, retryCount + 1); // 递归调用，增加重试次数
                } else {
                    sseEmitter.completeWithError(new Exception("Request failed after 3 retries " + e.getMessage()));
                }
            }
        });
        executor.shutdown();
        return sseEmitter;
    }

   static void retrySse(int retryCount, SseEmitter sseEmitter, Request request){
        if (retryCount < 3) { // 如果还有重试机会
            log.info("尝试重新请求，当前重试次数: {}", retryCount + 1);
            sseEmitter.completeWithError(new Exception("Request failed, retrying..."));
            streamRagRequest(request, retryCount + 1); // 递归调用，增加重试次数
        } else {
            sseEmitter.completeWithError(new Exception("Request failed after 3 retries " ));
        }
    }

    // 调用方法时需要传入初始重试次数0
    public static SseEmitter streamRagRequestRetry(Request request) {
        return streamRagRequest(request, 0);
    }




    /**
     * 发送Get请求
     *
     * @param url 请求地址
     * @return 响应体
     * @throws TyrfingServiceException 请求异常
     */
    public static byte[] get(String url) throws TyrfingServiceException {
        byte[] responseBodyStr;
        // 创建Request对象，指定URL
        Request request = new Request.Builder()
                .url(url)
                .build();
        log.info("<================= url: {} =================>", url);
        try {
            Response response = getOkHttpClient().newCall(request).execute();
            responseBodyStr = response.body() != null ? response.body().bytes() : null;
            if (response.code() != HttpStatus.OK.value()) {
                String responseBodyStrData="";
                if (response.body()!=null){
                     responseBodyStrData = response.body().string();
                }
                log.error("get请求异常, Http状态码: {}, responseBody: {}", response.code(), responseBodyStr);
                throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常 responseBodyStr:"+responseBodyStrData);
            }
            response.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
            throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常,err: "+e.getMessage());
        }
        log.info("<================= url: {}", url);
        return responseBodyStr;
    }

    /**
     * 获取OkHttpClient对象
     *
     * @return OkHttpClient对象
     */
    public static OkHttpClient getOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        int time = 60;
        // 设置连接超时时间
        builder.connectTimeout(time, TimeUnit.SECONDS);
        // 设置读取超时时间
        builder.readTimeout(time, TimeUnit.SECONDS);
        // 设置写入超时时间
        builder.writeTimeout(time, TimeUnit.SECONDS);
        return builder.build();
    }


//    public static String checkText(String text, String sessionId) throws TyrfingServiceException {
//
//        try {
//            return JsonUtil.toJsonString(TextAutoRoute(text, sessionId));
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//
//    }

//    private static TextModerationResponseBody.TextModerationResponseBodyData TextAutoRoute(String text, String sessionId) throws Exception {
//        Config config = new Config();
//        /**
//         * 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
//         * 常见获取环境变量方式：
//         * 方式一：
//         *     获取RAM用户AccessKey ID：System.getenv("ALIBABA_CLOUD_ACCESS_KEY_ID");
//         *     获取RAM用户AccessKey Secret：System.getenv("ALIBABA_CLOUD_ACCESS_KEY_SECRET");
//         * 方式二：
//         *     获取RAM用户AccessKey ID：System.getProperty("ALIBABA_CLOUD_ACCESS_KEY_ID");
//         *     获取RAM用户AccessKey Secret：System.getProperty("ALIBABA_CLOUD_ACCESS_KEY_SECRET");
//         */
//        config.setAccessKeyId("LTAI5tCVZY6mjYH5faUZhX2b");
//        config.setAccessKeySecret("******************************");
//        //接入区域和地址请根据实际情况修改
//        config.setRegionId("cn-hangzhou");
//        config.setEndpoint("green-cip.cn-hangzhou.aliyuncs.com");
//        //连接时超时时间，单位毫秒（ms）。
//        config.setReadTimeout(6000);
//        //读取时超时时间，单位毫秒（ms）。
//        config.setConnectTimeout(3000);
//        //设置http代理。
//        //config.setHttpProxy("http://10.10.xx.xx:xxxx");
//        //设置https代理。
//        //config.setHttpsProxy("https://10.10.xx.xx:xxxx");
//        // 注意，此处实例化的client请尽可能重复使用，避免重复建立连接，提升检测性能
//        Client client = new Client(config);
//
//        //ValidateUtil.validateNotEmpty(text, "输入信息不能为空");
//        ValidateUtil.validateNotEmpty(sessionId, "sessionId不能为空");
//
//        // 创建RuntimeObject实例并设置运行参数。
//        RuntimeOptions runtime = new RuntimeOptions();
//        runtime.readTimeout = 10000;
//        runtime.connectTimeout = 10000;
//
//        //检测参数构造
//        JSONObject serviceParameters = new JSONObject();
//        serviceParameters.put("content", text);
//        serviceParameters.put("sessionId", sessionId);
//
//
//        TextModerationRequest textModerationRequest = new TextModerationRequest();
//        /*
//        文本检测service：内容安全控制台文本增强版规则配置的serviceCode，示例：chat_detection
//        */
//        textModerationRequest.setService("ai_art_detection");
//        textModerationRequest.setServiceParameters(serviceParameters.toJSONString());
//        try {
//            // 调用方法获取检测结果。
//            TextModerationResponse response = client.textModerationWithOptions(textModerationRequest, runtime);
//            // 自动路由。
//            if (response != null) {
//                // 服务端错误，区域切换到cn-beijing。
//                if (500 == response.getStatusCode() || (response.getBody() != null && 500 == (response.getBody().getCode()))) {
//                    // 接入区域和地址请根据实际情况修改。
//                    config.setRegionId("cn-beijing");
//                    config.setEndpoint("green-cip.cn-beijing.aliyuncs.com");
//                    client = new Client(config);
//                    response = client.textModerationWithOptions(textModerationRequest, runtime);
//                }
//
//            }
//            // 打印检测结果。
//            if (response != null) {
//                if (response.getStatusCode() == 200) {
//                    TextModerationResponseBody result = response.getBody();
//                    System.out.println(JSON.toJSONString(result));
//                    Integer code = result.getCode();
//                    if (code != null && code == 200) {
//                        TextModerationResponseBody.TextModerationResponseBodyData data = result.getData();
//                        System.out.println("labels = [" + data.getLabels() + "]");
//                        System.out.println("reason = [" + data.getReason() + "]");
//                        return data;
//                    } else {
//                        log.error("text moderation not success. code:" + code);
//                    }
//                } else {
//                    log.error("text moderation not success. status:" + response.getStatusCode());
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return new TextModerationResponseBody.TextModerationResponseBodyData();
//    }


    /**
     * 发送Post请求
     *
     * @param url    请求地址
     * @param body   请求体
     * @param headers 请求头
     * @return 响应体
     * @throws TyrfingServiceException 请求异常
     */
    public static byte[] post(String url, String body, Map<String, String> headers) throws TyrfingServiceException {
        byte[] responseBodyBytes;
        RequestBody reqBody = RequestBody.create(MediaType.parse("application/json"), body);
        Request.Builder requestBuilder = new Request.Builder().url(url);
        
        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        
        Request request = requestBuilder.post(reqBody).build();
        try {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            int time = 60;
            // 设置连接超时时间
            builder.connectTimeout(time, TimeUnit.SECONDS);
            // 设置读取超时时间
            builder.readTimeout(time, TimeUnit.SECONDS);
            // 设置写入超时时间
            builder.writeTimeout(time, TimeUnit.SECONDS);
            OkHttpClient client = builder.build();
            Response response = client.newCall(request).execute();
            ResponseBody responseBody = response.body();
            responseBodyBytes = responseBody != null ? responseBody.bytes() : new byte[0];
            if (response.code() != HttpStatus.OK.value()) {
                log.error("post请求异常, Http状态码: {}, 请求体: {}, url: {}", response.code(), body, url);
                throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常");
            }
            response.close();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常 " + e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new TyrfingServiceException(TyrfingErrorCode.EXTERNAL_SYSTEM_ERROR, "系统发送请求异常 " + e.getMessage());
        }
        return responseBodyBytes;
    }

}
