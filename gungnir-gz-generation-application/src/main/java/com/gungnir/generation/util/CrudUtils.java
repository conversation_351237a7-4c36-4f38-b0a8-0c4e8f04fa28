package com.gungnir.generation.util;


import com.gungnir.generation.mapper.mongo.entity.DocumentTask;
import com.gungnir.generation.mapper.mongo.entity.RunningTask;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Sorts;
import com.mongodb.client.result.UpdateResult;
import com.tyrfing.common.TyrfingErrorCode;
import com.tyrfing.exceptions.TyrfingServiceException;
import com.tyrfing.model.Pagination;
import com.tyrfing.mongo.BaseEntity;
import com.tyrfing.mongo.TyrfingMongoDb;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Component
public class CrudUtils {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private static final TyrfingErrorCode lackOfParameter = TyrfingErrorCode.LACK_OF_PARAMETER;
    public static final String INSERT_TIME = "insertTime";
    public static final String UPDATE_TIME = "updateTime";
    public static final String DELETE_TIME = "deleteTime";
    public static final String DELETED = "deleted";

    private final TyrfingMongoDb mongoDb;

    public CrudUtils(TyrfingMongoDb mongoDb) {
        this.mongoDb = mongoDb;
    }

    private MongoTemplate getMongoTemplate() throws TyrfingServiceException {
        try {
            return mongoDb.getTemplate();
        } catch (Exception e) {
            e.printStackTrace();
            throw new TyrfingServiceException(TyrfingErrorCode.MONGO_EXECUTION_ERROR, "操作失败. "+e.getMessage());
        }
    }

    /**
     * 获取条件表达式
     *
     * @return 条件表达式
     */
    public Criteria getCriteria() {
        return where(DELETED).is(false);
    }

    /**
     * 保存数据
     *
     * @param object extends BaseEntity
     * @return object extends BaseEntity
     */
    public <T extends BaseEntity> T save(T object) throws TyrfingServiceException {
       // object.setInsertTime(System.currentTimeMillis());
        //object.setUpdateTime(object.getInsertTime());
        return this.getMongoTemplate().save(object);
    }

    /**
     * 删除文档
     *
     * @param match      匹配规则
     * @param domainType 类名
     */
    public <T> void deleteDocument(Criteria match, Class<T> domainType) throws TyrfingServiceException {
        this.getMongoTemplate().remove(new Query(match), domainType);
    }

    /**
     * 检查并更新文档
     * 可传递一个属性为最新的对象，将新对象的非空属性覆盖数据库中老的属性，但无法置空老的非空属性
     *
     * @param object     BaseModel的子类对象
     * @param id         文档id
     * @param isIncluded true，fields为要检查是否为null的属性；false，fields为不用检查是否为null的属性
     * @param fields     属性列表
     */
    public <T extends BaseEntity> void checkAndUpdate(T object, String id, boolean isIncluded, String... fields) throws TyrfingServiceException {
        if (StringUtils.isBlank(id))
            throw new TyrfingServiceException(lackOfParameter, String.format("%s: id", lackOfParameter.getReasonPhrase()));

        if (isIncluded)
            baseModelFieldsAreNull(object, true, fields);
        else
            baseModelFieldsAreNullExcluded(object, true, fields);

        updateDocument(object, id);
    }

    /**
     * BaseModel的子类对象指定是否为null
     *
     * @param object  BaseModel的子类对象
     * @param isThrow 是否抛出异常
     * @param include 要检查是否为null的属性
     * @return boolean
     */
    public <T extends BaseEntity> boolean baseModelFieldsAreNull(T object, boolean isThrow, String... include) throws TyrfingServiceException {
        return !baseModelFieldsAreNotNull(object, isThrow, include);
    }

    /**
     * BaseModel的子类对象除指定属性之外的其他属性是否为null
     * 默认排除insertTime， updateTime， deleteTime， deleted;
     *
     * @param object  BaseModel的子类对象
     * @param isThrow 是否抛出异常
     * @param exclude 不用检查是否为null的属性
     * @return boolean
     */
    public <T extends BaseEntity> boolean baseModelFieldsAreNullExcluded(T object, boolean isThrow, String... exclude) throws TyrfingServiceException {
        return !baseModelFieldsAreNotNullExcluded(object, isThrow, exclude);
    }

    /**
     * BaseModel的子类对象指定是否不为null
     *
     * @param object  BaseModel的子类对象
     * @param isThrow 是否抛出异常
     * @param include 要检查是否为null的属性
     * @return boolean
     */
    public <T extends BaseEntity> boolean baseModelFieldsAreNotNull(T object, boolean isThrow, String... include) throws TyrfingServiceException {
        return fieldsAreNotNull(object, isThrow, include);
    }

    /**
     * BaseModel的子类对象除指定属性之外的其他属性是否不为null
     * 默认排除insertTime， updateTime， deleteTime， deleted;
     *
     * @param object  BaseModel的子类对象
     * @param isThrow 是否抛出异常
     * @param exclude 不用检查是否为null的属性
     * @return boolean
     */
    public <T extends BaseEntity> boolean baseModelFieldsAreNotNullExcluded(T object, boolean isThrow, String... exclude) throws TyrfingServiceException {
        return fieldsAreNotNullExcluded(object, isThrow, ArrayUtils.addAll(exclude, INSERT_TIME, UPDATE_TIME, DELETE_TIME, DELETED));
    }

    /**
     * 检查指定属性是否不为null
     * 可指定是否抛出异常
     *
     * @param object  对象
     * @param isThrow 是否抛出异常
     * @param include 要检查是否为null的属性
     * @return boolean
     */
    public boolean fieldsAreNotNull(Object object, boolean isThrow, String... include) throws TyrfingServiceException {
        try {
            if (null == object) {
                throw new TyrfingServiceException(lackOfParameter, lackOfParameter.getReasonPhrase());
            }
            for (Field f : object.getClass().getDeclaredFields()) {
                if (ArrayUtils.contains(include, f.getName())) {
                    f.setAccessible(true);
                    if (f.get(object) == null || (f.get(object) instanceof String && StringUtils.isBlank((String) f.get(object)))) {
                        throw new TyrfingServiceException(lackOfParameter, String.format("%s: %s", lackOfParameter.getReasonPhrase(), f.getName()));
                    }
                }
            }
        } catch (TyrfingServiceException e1) {
            if (BooleanUtils.isTrue(isThrow)) throw e1;
            return false;
        } catch (IllegalAccessException e2) {
            log.error(e2.getMessage(), e2);
        }
        return true;
    }

    /**
     * 检查对象除指定属性之外的其他属性是否不为null
     * 可指定是否抛出异常
     *
     * @param object  对象
     * @param isThrow 是否抛出异常
     * @param exclude 不用检查是否为null的属性
     * @return boolean
     */
    public boolean fieldsAreNotNullExcluded(Object object, boolean isThrow, String... exclude) throws TyrfingServiceException {
        try {
            if (null == object) {
                throw new TyrfingServiceException(lackOfParameter, lackOfParameter.getReasonPhrase());
            }
            for (Field f : object.getClass().getDeclaredFields()) {
                if (ArrayUtils.contains(exclude, f.getName())) {
                    continue;
                }
                f.setAccessible(true);
                if (f.get(object) == null || (f.get(object) instanceof String && StringUtils.isBlank((String) f.get(object)))) {
                    throw new TyrfingServiceException(lackOfParameter, String.format("%s: %s", lackOfParameter.getReasonPhrase(), f.getName()));
                }
            }
        } catch (TyrfingServiceException e1) {
            if (BooleanUtils.isTrue(isThrow)) throw e1;
            return false;
        } catch (IllegalAccessException e2) {
            log.error(e2.getMessage(), e2);
        }
        return true;
    }

    /**
     * 更新文档
     * 可传递一个属性为最新的对象，将新对象的非空属性覆盖数据库中老的属性，但无法置空老的非空属性
     *
     * @param object 更新的对象
     * @param id     更新的对象id
     */
    public void updateDocument(BaseEntity object, String id) throws TyrfingServiceException {
        object.setUpdateTime(System.currentTimeMillis());
        UpdateResult updateResult = this.getMongoTemplate()
                .update(object.getClass())
                .matching(Query.query(where("id").is(id)))
                .apply(Update.fromDocument(new Document("$set", object)))
                .first();
        if (updateResult.getModifiedCount() != 1) {
            throw new TyrfingServiceException(TyrfingErrorCode.MONGO_EXECUTION_ERROR, "更新失败");
        }
    }

    /**
     * 更新文档
     * 可更新指定域，置空属性
     *
     * @param match      匹配规则
     * @param update     update对象
     * @param domainType 更新的类
     * @return String "更新成功"
     */
    public <T> String updateDocument(Criteria match, Update update, Class<T> domainType) throws TyrfingServiceException {
        UpdateResult updateResult = this.getMongoTemplate()
                .update(domainType)
                .matching(Query.query(match))
                .apply(update.set(UPDATE_TIME, System.currentTimeMillis())).all();
        log.info("ModifiedCount={}", updateResult.getModifiedCount());
        return "更新成功";
    }

    /**
     * 更新文档
     * 将老文档全部替换为新文档
     *
     * @param object 对应实体对象
     * @param <T>    实体类型
     * @return 更新后的实体对象
     * @throws TyrfingServiceException 业务异常
     */
    public <T extends BaseEntity> T updateDocument(T object) throws TyrfingServiceException {
        object.setUpdateTime(object.getInsertTime());
        return this.getMongoTemplate().save(object);
    }

    /**
     * 转换pagination
     *
     * @param page page
     * @param <T>  对应实体
     * @return pagination
     */
    public <T extends Serializable> Pagination<T> convertPagination(Page<T> page) {
        Pagination<T> pagination = new Pagination<>();
        pagination.setData(page.getContent());
        pagination.setPageNum(page.getNumber());
        pagination.setPageSize(page.getSize());
        pagination.setTotal(page.getTotalElements());
        return pagination;
    }

    public void save(DocumentTask entity) throws TyrfingServiceException {
         this.getMongoTemplate().save(entity);

    }

    public String getUpdateStatus(String courseId,MongoTemplate mongoTemplate) throws TyrfingServiceException {
        Document filter = new Document("courseId", courseId);
        //投影；只要status字段，不要_id
        Document projection = new Document("status", 1).append("_id", 0);

        // 查询集合
//        MongoCollection<Document> collection = this.getMongoTemplate().getCollection("documentTask");
        MongoCollection<Document> collection = mongoTemplate.getCollection("documentTask");

        //查询并排序、限制结果为1条
        Document result = collection.find(filter)
                .projection(projection)
                .sort(Sorts.descending("insertTime"))
                .limit(1)
                .first();

        if (result!=null) {
            return result.getString("status");
        }else
            return null;
    }

    public List<RunningTask> getRunningTask(String status, MongoTemplate mongoTemplate){

        List<RunningTask> runningTasks = new ArrayList<>();

        // 1. 构造查询条件
        Document filter = new Document("status", status);

        // 2. 只返回 _id 和 insertTime 字段
        Document projection = new Document("_id", 1).append("insertTime", 1);

        // 3. 获取集合
        MongoCollection<Document> collection = mongoTemplate.getCollection("documentTask");

        // 4. 执行查询
        FindIterable<Document> docs  = collection.find(filter)
                .projection(projection);

        // 5. 遍历转换为 RunningTask
        for (Document doc : docs) {
            String id = doc.getObjectId("_id").toHexString(); // 或用 toString()

            Date insertTimeDate = doc.getDate("insertTime");
            LocalDateTime insertTime = null;
            if (insertTimeDate != null) {
                insertTime = insertTimeDate.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
            }            RunningTask runningTask = new RunningTask();
            runningTask.setId(id);
            runningTask.setInsertTime(insertTime);
            runningTasks.add(runningTask);
        }

        return runningTasks;
    }



    public void updateTaskStatus(RunningTask runningTask, MongoTemplate mongoTemplate){
        // 1. 获取集合
        MongoCollection<Document> collection = mongoTemplate.getCollection("documentTask");

        // 2. 构造查询条件（注意 ObjectId 类型）
        Document filter = new Document("_id",  new org.bson.types.ObjectId(runningTask.getId()));

        // 3. 构造更新时间字段（转换为 Date 类型）
        Date updateTime = Date.from(LocalDateTime.now()
                .atZone(ZoneId.systemDefault())
                .toInstant());

        // 4. 构造更新内容
        Document update = new Document("$set", new Document("status", "失败")
                .append("updateTime", updateTime)
                .append("deleted", 1));
        // 5. 执行更新
        collection.updateOne(filter, update);

    }
}
