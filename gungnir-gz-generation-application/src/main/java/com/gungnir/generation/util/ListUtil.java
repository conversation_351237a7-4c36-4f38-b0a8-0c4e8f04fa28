package com.gungnir.generation.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ListUtil {
//    void d(Object [] objects){
//        List lst =new ArrayList<>(Arrays.asList(new String[] { "zs", "ls", "ww" }));
//        List lst =new ArrayList<>(Arrays.asList(objects));
//    }

    public  static  <T>  boolean  isNullOrEmpty(List<T>list){
        return  list==null||list.isEmpty();
    }

    public static <T> List<T> arrToArrayList(T... a) {
        List<T> lst =new ArrayList<>(Arrays.asList(a));
//        return new Arrays.ArrayList<>(a);
        return lst;
    }
    public static <T> T  getLast(List<T> list){
        if (list==null||list.isEmpty()) {
            return null;
        }
        T lastElement = list.get(list.size() - 1);
        return lastElement;
    }

    public static <T> T  getFirst(List<T> list){
        if (list==null||list.isEmpty()) {
            return null;
        }
        T lastElement = list.get(0);
        return lastElement;
    }

//    public static <T> T  getFirst(Collection<T> list){
////        list.em
//        if (list==null||list.isEmpty()) {
//            return null;
//        }
////        list.
//        T lastElement = list.get(0);
//        return lastElement;
//    }
    public static <T> int  getSize(List<T> list){
        if (list==null||list.isEmpty()) {
            return 0;
        }
        return  list.size();
    }
    /**
     * list
     * [zs, ls, ww]
     * list
     * [zs, ls, ww, ddd]
     * @param args
     */
    public static void main1(String[] args) {
//        new ArrayList<Integer>{1,3,5}
        String[] strings=    new String[] { "zs", "ls", "ww" };
        List<String> list = ListUtil.arrToArrayList(strings);
        System.out.println("list");
        System.out.println(list);

        list.add("ddd");
        System.out.println("list");
        System.out.println(list);
//        list
//                [zs, ls, ww]
//        list
//                [zs, ls, ww, ddd]
    }
}

