package com.gungnir.generation.util;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tyrfing.model.Pagination;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PageUtil {

    /**
     * 分页集合转换
     *
     * @param page        转换前的分页集合
     * @param mappingFunc 函数
     * @param <T>         转换前对象类型
     * @param <R>         转换后对象类型
     * @return 转换后的分页集合
     */
    public static <T extends Serializable, R extends Serializable> Pagination<R> transform(Pagination<T> page, Function<T, R> mappingFunc) {
        Pagination<R> retPage = new Pagination<>(page.getPageSize(), page.getPageNum());
        retPage.setHeader(page.getHeader());
        retPage.setTotal(page.getTotal());
        retPage.setData(mapping(page.getData(), mappingFunc));
        return retPage;
    }

    /**
     * 分页集合转换
     *
     * @param page        转换前的分页集合
     * @param mappingFunc 函数
     * @param <T>         转换前对象类型
     * @param <R>         转换后对象类型
     * @return 转换后的分页集合
     */
    public static <T extends Serializable, R extends Serializable> Pagination<R> transform(Page<T> page, Function<T, R> mappingFunc) {
        Pagination<R> retPage = new Pagination<>((int) page.getSize(), (int) page.getCurrent());
        retPage.setTotal(page.getTotal());
        retPage.setData(mapping(page.getRecords(), mappingFunc));
        return retPage;
    }

    /**
     * 分页集合转换
     *
     * @param page  转换前的分页集合
     * @param clazz 转换后的Class对象
     * @param <T>   转换前对象类型
     * @param <R>   转换后对象类型
     * @return 转换后的分页集合
     */
    public static <T extends Serializable, R extends Serializable> Pagination<R> transform(Page<T> page, Class<R> clazz) {
        return transform(page, e -> BeanUtil.toBean(e, clazz));
    }

    public static  <T extends Serializable> Pagination<T>  toPagination(IPage<T> page){
//        Pagination<T>pagination=new Pagination<T>();
//        List<T> records = page.getRecords();
//        pagination.setData(records);
//        pagination.setTotal(page.getTotal());
//        pagination.setPageNum((int) page.getCurrent());
//        pagination.setPageSize((int) page.getSize());
//        return pagination;

        return  mybatisPlusPageToTyfingPage(page);
    }

    public  static  <T extends Serializable>   Pagination<T> mybatisPlusPageToTyfingPage(IPage<T> page ){
        Pagination<T> retPage = new Pagination<>((int) page.getSize(), (int) page.getCurrent());
        retPage.setTotal(page.getTotal());
        retPage.setData(page.getRecords());
        return  retPage;
    }
    public  static  <T extends Serializable>   Pagination<T> toTyfingPage( org.springframework.data.domain.Page<T> page ){
//        import org.springframework.data.domain.Page;

//        int totalPages = page.getTotalPages();
        long totalPages = page.getTotalElements();
        int size = page.getSize();
        List<T> content = page.getContent();
        int number = page.getNumber()+1;

        Pagination<T> retPage = new Pagination<>(size,  number);
        retPage.setTotal(totalPages);
        retPage.setData(content);

        return  retPage;
    }

    /**
     * 集合函数式转换
     *
     * @param list        转换前集合
     * @param mappingFunc 函数
     * @param <T>         转换前对象类型
     * @param <R>         转换后对象类型
     * @return 转换后集合
     */
    private static <T, R> List<R> mapping(List<T> list, Function<T, R> mappingFunc) {
        return Optional.ofNullable(list).orElseGet(Collections::emptyList).stream()
                .map(mappingFunc).collect(Collectors.toList());
    }

}
