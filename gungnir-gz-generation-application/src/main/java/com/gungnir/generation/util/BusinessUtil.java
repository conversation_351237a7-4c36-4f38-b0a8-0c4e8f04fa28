package com.gungnir.generation.util;

import com.tyrfing.common.TyrfingErrorCode;
import com.tyrfing.contexts.TyrfingTokenContext;
import com.tyrfing.exceptions.TyrfingServiceException;
import com.tyrfing.model.auth.entity.AcctUser;
import com.tyrfing.utils.JWTUtil;
import org.springframework.stereotype.Component;


@Component
public class BusinessUtil {

    /**
     * 获取用户信息
     *
     * @return 用户信息
     * @throws TyrfingServiceException 异常
     */
    public AcctUser getUser() throws TyrfingServiceException {
        AcctUser user = new AcctUser();
        try {
            String userSid = JWTUtil.getUserSid(TyrfingTokenContext.getOrThrow());
            user.setSid(userSid);
        } catch (Exception e) {
            throw new TyrfingServiceException(TyrfingErrorCode.NEO4J_EXECUTION_ERROR, "userDetail==null,用户服务出错 " + e.getMessage());
        }
        return user;
    }

}
