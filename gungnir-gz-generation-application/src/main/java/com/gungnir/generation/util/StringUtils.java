package com.gungnir.generation.util;

import java.util.Date;

public class StringUtils extends org.apache.commons.lang3.StringUtils {
  public  static String  getAuthorizationToken(String authorizationHeader){
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            String token = authorizationHeader.substring(7);
            return token;
        }
        return authorizationHeader;
    }


    public static void printException(Exception ex){

        String message = ex.getMessage();
        Date date = new Date();

        System.err.println("===========  {date} err =========".replace("{date}", String.valueOf(date)));
        StackTraceElement[] stackTrace = ex.getStackTrace();
        System.err.println("err message");
        System.err.println(message);

        int idx=0;
        for (StackTraceElement stackTraceElement : stackTrace) {
            String className = stackTraceElement.getClassName();
            if(idx<=3){
                System.err.println(stackTraceElement);
            }else      if (className.contains("starp")
                    ||className.contains("gungnir")) {
                System.err.println(stackTraceElement);
            }
            idx++;
        }

        System.err.println("=========== err  up  =========");

    }
    public static String format(String format, Object... args) {
        int idx=0;
        for (Object arg : args) {

            format= format.replace("{{idx}}".replace("{idx}", String.valueOf(idx)), String.valueOf(arg));
            idx++;
        }
        return  format;
    }



}
