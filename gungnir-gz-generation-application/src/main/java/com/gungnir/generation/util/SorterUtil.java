package com.gungnir.generation.util;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @title
 */
public class SorterUtil<T> {
    /**
     * 将传进来的对象，根据fieldName字段来进行降序排列
     *
     * @param list      List数组
     * @param fieldName 根据这个字段进行排序
     * @return List<T>
     */
    public List<T> sortByDescField(List<T> list, String fieldName) {
        Collections.sort(list, new Comparator<T>() {
            @Override
            public int compare(T d1, T d2) {
                try {
                    Field field = d1.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    Comparable fieldValue1 = (Comparable) field.get(d1);
                    Comparable fieldValue2 = (Comparable) field.get(d2);
                    return fieldValue2.compareTo(fieldValue1);//降序排列
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    e.printStackTrace();
                    return 0;
                }

            }
        });
        return list;
    }
}
