package com.gungnir.generation.util;

import com.tyrfing.common.TyrfingErrorCode;
import com.tyrfing.exceptions.TyrfingServiceException;
import org.springframework.util.ObjectUtils;

public class ValidateUtil {

    /**
     * 校验参数为空
     *
     * @param value    值
     * @param errorMsg 错误信息
     * @param <T>      值类型
     * @throws TyrfingServiceException 业务异常
     */
    public static <T> void validateEmpty(T value, String errorMsg) throws TyrfingServiceException {
        if (!ObjectUtils.isEmpty(value)) {
            throw new TyrfingServiceException(TyrfingErrorCode.LACK_OF_PARAMETER, errorMsg);
        }
    }

    /**
     * 校验参数不为空
     *
     * @param value    值
     * @param errorMsg 错误信息
     * @param <T>      值类型
     * @throws TyrfingServiceException 业务异常
     */
    public static <T> void validateNotEmpty(T value, String errorMsg) throws TyrfingServiceException {
        if (ObjectUtils.isEmpty(value)) {
            throw new TyrfingServiceException(TyrfingErrorCode.LACK_OF_PARAMETER, errorMsg);
        }
    }

}
