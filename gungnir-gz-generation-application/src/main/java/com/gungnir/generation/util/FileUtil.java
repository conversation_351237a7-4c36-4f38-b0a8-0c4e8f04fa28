package com.gungnir.generation.util;


import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;

public class FileUtil {

    private static final Logger log = LoggerFactory.getLogger("FileUtil");

    /**
     * 获取doc文件内容
     *
     * @param inputStream 文件流
     * @return
     */
    public static String extractTextFromDoc(InputStream inputStream) throws IOException, InvalidFormatException {
        // 这是一个Word文件
        XWPFWordExtractor extractor = new XWPFWordExtractor(new XWPFDocument(inputStream));
        return extractor.getText();
    }


    /**
     * 获取pdf中的文本
     *
     * @param inputStream 文件流
     * @return 文本
     **/
    public static String extractTextFromPDF(InputStream inputStream) {
        try {
            PDDocument document = PDDocument.load(inputStream);
            PDFTextStripper pdfStripper = new PDFTextStripper();
            return pdfStripper.getText(document);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取docx中的文本
     *
     * @param in 文件流
     * @return 文本
     **/
    private static String extractDocx(InputStream in) {
        XWPFDocument xdoc = null;
        try {
            xdoc = new XWPFDocument(in);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        XWPFWordExtractor extractor = new XWPFWordExtractor(xdoc);
        return extractor.getText();
    }

//    /**
//     * 获取xls中的文本
//     *
//     * @param in 文件流
//     * @return 文本
//     **/
//    public static String extractXls(InputStream in) {
//        Workbook workbook = null;
//        try {
//            workbook = new HSSFWorkbook(in);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        return workbook2String(workbook);
//    }
//
//    /**
//     * 获取xlsx中的文本
//     *
//     * @param in 文件流
//     * @return 文本
//     **/
//    public static String extractXlsx(InputStream in) {
//        Workbook workbook = null;
//        try {
//            workbook = new XSSFWorkbook(in);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        }
//        return workbook2String(workbook);
//    }
//
//    private static String workbook2String(Workbook workbook) {
//        StringBuffer sb = new StringBuffer();
//        if (workbook != null) {
//            for (int sheetNum = 0; sheetNum < workbook.getNumberOfSheets(); sheetNum++) {
//                // 获得当前sheet工作表
//                Sheet sheet = workbook.getSheetAt(sheetNum);
//                if (sheet == null) {
//                    continue;
//                }
//                // 获得当前sheet的开始行
//                int firstRowNum = sheet.getFirstRowNum();
//                // 获得当前sheet的结束行
//                int lastRowNum = sheet.getLastRowNum();
//                // 循环除了第一行的所有行
//                sb.append(sheet.getSheetName() + "\n");
//                for (int rowNum = firstRowNum; rowNum <= lastRowNum; rowNum++) {
//                    // 获得当前行
//                    Row row = sheet.getRow(rowNum);
//                    if (row == null || row.getPhysicalNumberOfCells() == 0) {
//                        /* String[] a=new String[0]; list.add(a); */
//                        // sb.append("\r\n");
//                        continue;
//                    } // 获得当前行的开始列
//                    int firstCellNum = row.getFirstCellNum();
//                    // 获得当前行的列数
//                    int lastCellNum = row.getLastCellNum();
//                    // String[] cells = new
//                    // String[row.getPhysicalNumberOfCells()];
//                    // 循环当前行
//                    for (int cellNum = firstCellNum; cellNum < lastCellNum; cellNum++) {
//                        Cell cell = row.getCell(cellNum);
//                        String value = cellValue(cell);
//                        if (!"".equals(value)) {
//                            sb.append(value);
//                            sb.append(",");
//                        }
//                    }
//                }
//                sb.append("\n");
//            }
//        }
//        return sb.toString();
//    }
//
//    /**
//     * 获得Excel格子里的内容
//     *
//     * @param cell 每个格子
//     * @return
//     */
//    private static String cellValue(Cell cell) {
//        String cellValue = "";
//        if (cell == null) {
//            return cellValue;
//        }
//        // 把数字当成String来读，避免出现1读成1.0的情况
//        // if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
//        // // cell.setCellType(Cell.CELL_TYPE_STRING);
//        //
//        // }
//        // 判断数据的类型
//        switch (cell.getCellType()) {
//            case Cell.CELL_TYPE_NUMERIC: // 数字
//                // cellValue = String.valueOf(cell.getNumericCellValue());
//                short format = cell.getCellStyle().getDataFormat();
//                SimpleDateFormat sdf = null;
//                if (format == 14 || format == 31 || format == 57 || format == 58
//                        || (176 <= format && format <= 178)
//                        || (182 <= format && format <= 196)
//                        || (210 <= format && format <= 213) || (208 == format)) {
//                    if (format == 14) {
//                        sdf = new SimpleDateFormat("yyyy/MM/dd");
//                    } else {
//                        sdf = new SimpleDateFormat("yyyy年MM月dd日");
//                    }
//
//                    double value = cell.getNumericCellValue();
//                    Date date = org.apache.poi.ss.usermodel.DateUtil
//                            .getJavaDate(value);
//                    if (date == null || "".equals(date)) {
//                        cellValue = "";
//                    }
//                    try {
//                        cellValue = (sdf.format(date));
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                        cellValue = "";
//                    }
//                } else { // 不是日期格式
//                    cell.setCellType(Cell.CELL_TYPE_STRING);
//                    String temp = cell.getStringCellValue();
//                    // 判断是否包含小数点，如果不含小数点，则以字符串读取，如果含小数点，则转换为Double类型的字符串
//                    if (temp.indexOf(".") > -1) {
//                        cellValue = (String.valueOf(new Double(temp)).trim());
//                    } else {
//                        cellValue = (temp.trim());
//                    }
//                }
//                break;
//            case Cell.CELL_TYPE_STRING: // 字符串
//                cellValue = String.valueOf(cell.getStringCellValue());
//                break;
//            case Cell.CELL_TYPE_BOOLEAN: // Boolean
//                cellValue = String.valueOf(cell.getBooleanCellValue());
//                break;
//            case Cell.CELL_TYPE_FORMULA: // 公式
//                cellValue = String.valueOf(cell.getCellFormula());
//                break;
//            case Cell.CELL_TYPE_BLANK: // 空值
//                cellValue = "";
//                break;
//            case Cell.CELL_TYPE_ERROR: // 故障
//                cellValue = "";// 非法字符
//                break;
//            default:
//                cellValue = "";// 未知类型
//                break;
//        }
//        return cellValue;
//    }
//
//    /**
//     * 获取ppt中的文本
//     *
//     * @param in 文件流
//     * @return 文本
//     **/
//    private static String extractPpt(InputStream in) {
//        StringBuffer content = new StringBuffer();
//        SlideShow ss = new SlideShow(new HSLFSlideShow(in));
//        Slide[] slides = ss.getSlides();// 获得每一张幻灯片
//        for (int i = 0; i < slides.length; i++) {
//            TextRun[] t = slides[i].getTextRuns();// 为了取得幻灯片的文字内容，建立TextRun
//            for (int j = 0; j < t.length; j++) {
//                content.append(t[j].getText());// 这里会将文字内容加到content中去
//            }
//            content.append(slides[i].getTitle());
//        }
//        return content.toString();
//    }
//
//    /**
//     * 获取pptx中的文本
//     *
//     * @param in 文件流
//     * @return 文本
//     **/
//    private static String extractPptx(InputStream in) {
//        XMLSlideShow slide = new XMLSlideShow(in);
//        XSLFPowerPointExtractor extractor = new XSLFPowerPointExtractor(
//                slide);
//        return extractor.getText();
//    }
//
//
//    private static String extractPdf(InputStream in) {
//        RandomAccess rf = null;
//        PDDocument document = null;
//        try {
//            rf = new RandomAccessFile(new File(filePath), "r");
//            PDFParser parser = new PDFParser(rf);
//            parser.parse();
//            document = parser.getPDDocument();
//            PDFTextStripper stripper = new PDFTextStripper();
//            String text = stripper.getText(document);
//            document.close();
//            return text;
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            if (document != null) {
//                try {
//                    document.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//            if (rf != null) {
//                try {
//                    rf.close();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//        return null;
//    }
//
//
//
//
//    /**
//     * 获取xml中的文本
//     *
//     * @param in 文件流
//     * @return 文本
//     **/
//    private static String extractXml(InputStream in) {
//        Document doc = new SAXReader().read(in);
//        return doc.asXML();
//    }

}
