package com.gungnir.generation.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.gungnir.generation.VoiceTemplateService;
import com.gungnir.generation.mapper.mysql.mapper.VoiceTemplateMapper;
import com.gungnir.generation.mapper.mysql.table.VoiceTemplateTb;
import org.apache.dubbo.config.annotation.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2025年05月15日09:43
 * 邮件: <EMAIL>
 * 作者: AlanQuain
 */
@Service
public class VoiceTemplateServiceImpl implements VoiceTemplateService {

    private VoiceTemplateMapper voiceTemplateMapper;

    public VoiceTemplateServiceImpl(VoiceTemplateMapper voiceTemplateMapper) {
        this.voiceTemplateMapper = voiceTemplateMapper;
    }

    @Override
    public String demo() {
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectAll();
        return voiceTemplateTbs.toString();
    }

    @Override
    public List<Map<String, Object>> queryAll() {
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectAll();
        return convertToMapList(voiceTemplateTbs);
    }

    @Override
    public Map<String, Object> queryById(Long actorId) {
        VoiceTemplateTb voiceTemplateTb = voiceTemplateMapper.selectOneById(actorId);
        return voiceTemplateTb != null ? convertToMap(voiceTemplateTb) : null;
    }

    @Override
    public List<Map<String, Object>> queryByName(String actorName) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .like("actor_name", actorName);
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectListByQuery(queryWrapper);
        return convertToMapList(voiceTemplateTbs);
    }

    @Override
    public List<Map<String, Object>> queryByType(Integer type) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("type", type);
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectListByQuery(queryWrapper);
        return convertToMapList(voiceTemplateTbs);
    }

    @Override
    public List<Map<String, Object>> queryByLanguage(String language) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("language", language);
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectListByQuery(queryWrapper);
        return convertToMapList(voiceTemplateTbs);
    }

    @Override
    public List<Map<String, Object>> queryByStatus(Boolean status) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("status", status);
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectListByQuery(queryWrapper);
        return convertToMapList(voiceTemplateTbs);
    }

    @Override
    public List<Map<String, Object>> queryByCondition(String language, Integer type, Boolean status) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        if (language != null) {
            queryWrapper.eq("language", language);
        }
        if (type != null) {
            queryWrapper.eq("type", type);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        List<VoiceTemplateTb> voiceTemplateTbs = voiceTemplateMapper.selectListByQuery(queryWrapper);
        return convertToMapList(voiceTemplateTbs);
    }

    /**
     * 将VoiceTemplateTb对象转换为Map
     *
     * @param voiceTemplateTb VoiceTemplateTb对象
     * @return Map对象
     */
    private Map<String, Object> convertToMap(VoiceTemplateTb voiceTemplateTb) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", voiceTemplateTb.getId());
        map.put("actorName", voiceTemplateTb.getActorName());
        map.put("actorDescription", voiceTemplateTb.getActorDescription());
        map.put("avatar", voiceTemplateTb.getAvatar());
        map.put("voiceType", voiceTemplateTb.getVoiceType());
        map.put("language", voiceTemplateTb.getLanguage());
        map.put("exampleUrl", voiceTemplateTb.getExampleUrl());
        map.put("type", voiceTemplateTb.getType());
        map.put("sort", voiceTemplateTb.getSort());
        map.put("status", voiceTemplateTb.getStatus());
        map.put("insertTime", voiceTemplateTb.getInsertTime());
        return map;
    }

    /**
     * 将VoiceTemplateTb对象列表转换为Map列表
     *
     * @param voiceTemplateTbs VoiceTemplateTb对象列表
     * @return Map列表
     */
    private List<Map<String, Object>> convertToMapList(List<VoiceTemplateTb> voiceTemplateTbs) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (voiceTemplateTbs != null && !voiceTemplateTbs.isEmpty()) {
            for (VoiceTemplateTb voiceTemplateTb : voiceTemplateTbs) {
                mapList.add(convertToMap(voiceTemplateTb));
            }
        }
        return mapList;
    }
}
