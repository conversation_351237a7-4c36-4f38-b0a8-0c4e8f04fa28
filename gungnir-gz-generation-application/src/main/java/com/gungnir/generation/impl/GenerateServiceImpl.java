package com.gungnir.generation.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gungnir.generation.GenerateService;
import com.gungnir.generation.mapper.mysql.mapper.PptContentTextMapper;
import com.gungnir.generation.mapper.mysql.mapper.VideoTemplateMapper;
import com.gungnir.generation.mapper.mysql.mapper.VoiceSynthesisMapper;
import com.gungnir.generation.mapper.mysql.mapper.VoiceTemplateMapper;
import com.gungnir.generation.mapper.mysql.table.PptContentTextTb;
import com.gungnir.generation.mapper.mysql.table.VideoTemplateTb;
import com.gungnir.generation.mapper.mysql.table.VoiceSynthesisTb;
import com.gungnir.generation.mapper.mysql.table.VoiceTemplateTb;
import com.gungnir.generation.util.JsonUtil;
import com.gungnir.generation.util.OkHttpUtil;
import com.tyrfing.common.TyrfingErrorCode;
import com.tyrfing.component.fileupload.TyrfingFileuploadClient;
import com.tyrfing.exceptions.TyrfingServiceException;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR> 2025年05月16日10:24
 * 邮件: <EMAIL>
 * 作者: AlanQuain
 */
@Service
public class GenerateServiceImpl implements GenerateService {

    private static final Logger logger = LoggerFactory.getLogger(GenerateServiceImpl.class);
    
    private static final String CONTENT_TYPE_MP3 = "audio/mpeg";

    private PptContentTextMapper pptContentTextMapper;
    private VoiceTemplateMapper voiceTemplateMapper;
    private VideoTemplateMapper videoTemplateMapper;
    private VoiceSynthesisMapper voiceSynthesisMapper;
    private TyrfingFileuploadClient tyrfingFileuploadClient;
    private ObjectMapper objectMapper;

    public GenerateServiceImpl(PptContentTextMapper pptContentTextMapper, VoiceTemplateMapper voiceTemplateMapper,
                               VideoTemplateMapper videoTemplateMapper, VoiceSynthesisMapper voiceSynthesisMapper,
                               TyrfingFileuploadClient tyrfingFileuploadClient, ObjectMapper objectMapper) {
        this.pptContentTextMapper = pptContentTextMapper;
        this.voiceTemplateMapper = voiceTemplateMapper;
        this.videoTemplateMapper = videoTemplateMapper;
        this.voiceSynthesisMapper = voiceSynthesisMapper;
        this.tyrfingFileuploadClient = tyrfingFileuploadClient;
        this.objectMapper = objectMapper;
    }

    // 创建线程池用于异步处理
    private final ExecutorService executorService = new ThreadPoolExecutor(
            5, 10, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 计算音频时长（秒）
     * 注意：此方法适用于WAV格式，对MP3可能需要额外的库支持
     *
     * @param audioData 音频数据字节数组
     * @return 音频时长（秒）
     */
    private BigDecimal calculateAudioDuration(byte[] audioData) {
        try {
            // 尝试使用JavaSound API计算音频时长
            InputStream inputStream = new ByteArrayInputStream(audioData);
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(inputStream);
            AudioFormat format = audioInputStream.getFormat();
            long frames = audioInputStream.getFrameLength();
            
            // 计算音频时长（秒）
            double durationInSeconds = (frames / format.getFrameRate());
            return new BigDecimal(String.valueOf(durationInSeconds));
        } catch (UnsupportedAudioFileException e) {
            // 如果无法通过JavaSound API读取（比如MP3格式），使用估算方法
            logger.warn("无法直接读取音频格式，使用估算方法计算时长: {}", e.getMessage());
            return estimateMP3Duration(audioData);
        } catch (Exception e) {
            logger.error("计算音频时长出错: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 估算MP3音频时长
     * 这是一个粗略估算，实际时长可能有偏差
     *
     * @param mp3Data MP3数据
     * @return 估算的时长（秒）
     */
    private BigDecimal estimateMP3Duration(byte[] mp3Data) {
        try {
            // MP3的比特率通常是128kbps或其他值
            int assumedBitRate = 128 * 1024; // 128 kbps
            
            // 粗略估算公式：文件大小（比特）/ 比特率 = 时长（秒）
            double durationInSeconds = (mp3Data.length * 8.0) / assumedBitRate;
            
            // 由于这是估算值，可以根据经验进行修正
            // 例如，可以乘以一个修正系数来提高准确性
            double correctionFactor = 0.97; // 根据实际情况调整
            durationInSeconds *= correctionFactor;
            
            return new BigDecimal(String.valueOf(durationInSeconds));
        } catch (Exception e) {
            logger.error("估算MP3时长出错: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * 从API响应中解析音频时长
     * 如果API返回的JSON中包含时长信息，可以直接使用
     *
     * @param apiResponse API响应内容
     * @return 音频时长（秒）
     */
    private BigDecimal parseDurationFromApiResponse(String apiResponse) {
        try {
            // 假设API响应是JSON格式，并包含duration字段
            JsonNode responseJson = objectMapper.readTree(apiResponse);
            if (responseJson.has("duration")) {
                return new BigDecimal(responseJson.get("duration").asText());
            }
        } catch (Exception e) {
            logger.error("从API响应解析时长出错: {}", e.getMessage());
        }
        return BigDecimal.ZERO;
    }

    @Override
    public Boolean creationVideo(Integer pptId, Integer voiceTemplateId, Integer videoTemplateId) {
        try {
            // 1.先根据pptid查询出对应的PptContentText文本数据
            List<PptContentTextTb> pptContentTexts = pptContentTextMapper.selectByPptId(pptId);
            if (pptContentTexts == null || pptContentTexts.isEmpty()) {
                logger.error("找不到对应的PPT内容数据，pptId={}", pptId);
                return false;
            }
            
            // 2.再查询出voiceTemplateId对应的语音发音人相关数据
            VoiceTemplateTb voiceTemplate = voiceTemplateMapper.selectOneById(voiceTemplateId.longValue());
            if (voiceTemplate == null) {
                logger.error("找不到对应的语音模板数据，voiceTemplateId={}", voiceTemplateId);
                return false;
            }

            // 3.再查询出videoTemplateId对应的视频合成模版的数据
            VideoTemplateTb videoTemplate = videoTemplateMapper.selectOneById(videoTemplateId.longValue());
            if (videoTemplate == null) {
                logger.error("找不到对应的视频模板数据，videoTemplateId={}", videoTemplateId);
                return false;
            }
            
            // 4.调用VoiceTemplate中的voiceApiCode字段并按照其中的json来解析并发送api请求
            // 异步多线程发送,并将返回的mp3音频存在minio服务上并返回一个访问地址
            // 最后将数据存在voice_synthesis数据库表中
            String voiceApiCode = voiceTemplate.getVoiceApiCode();
            if (voiceApiCode == null || voiceApiCode.trim().isEmpty()) {
                logger.error("语音模板API配置为空，voiceTemplateId={}", voiceTemplateId);
                return false;
            }
            
            // 解析voiceApiCode中的JSON
            JsonNode apiConfig;
            try {
                apiConfig = objectMapper.readTree(voiceApiCode);
            } catch (Exception e) {
                logger.error("解析语音API配置失败: {}", e.getMessage());
                return false;
            }
            
            // 获取API必要参数
            String apiUrl = apiConfig.has("url") ? apiConfig.get("url").asText() : null;
            JsonNode headerNode = apiConfig.has("header") ? apiConfig.get("header") : null;
            JsonNode bodyNode = apiConfig.has("body") ? apiConfig.get("body") : null;
            
            if (apiUrl == null || headerNode == null || bodyNode == null) {
                logger.error("语音API配置不完整");
                return false;
            }
            
            // 创建任务列表
            List<Future<VoiceSynthesisTb>> futures = new ArrayList<>();
            
            // 遍历每一段PPT内容，进行语音合成
            for (PptContentTextTb pptContent : pptContentTexts) {
                String text = pptContent.getEditText() != null && !pptContent.getEditText().isEmpty() 
                        ? pptContent.getEditText() : pptContent.getText();
                if (text == null || text.trim().isEmpty()) {
                    continue;
                }
                
                // 提交异步任务
                futures.add(executorService.submit(() -> {
                    try {
                        // 准备API请求参数
                        Map<String, Object> requestParams = new HashMap<>();
                        
                        // 先处理JSON中的body部分
                        Iterator<String> fieldNames = bodyNode.fieldNames();
                        while (fieldNames.hasNext()) {
                            String fieldName = fieldNames.next();
                            String fieldValue = bodyNode.get(fieldName).asText();
                            
                            // 如果字段包含#TargetText#，则替换为实际的文本
                            if (fieldValue.contains("#TargetText#")) {
                                fieldValue = fieldValue.replace("#TargetText#", text);
                            }
                            requestParams.put(fieldName, fieldValue);
                        }
                        
                        // 设置API请求头
                        Map<String, String> headers = new HashMap<>();
                        headers.put("Content-Type", "application/json");
                        
                        // 如果header是数组，则遍历添加到headers中
                        if (headerNode.isArray()) {
                            for (JsonNode header : headerNode) {
                                Iterator<String> headerNames = header.fieldNames();
                                while (headerNames.hasNext()) {
                                    String headerName = headerNames.next();
                                    headers.put(headerName, header.get(headerName).asText());
                                }
                            }
                        }
                        
                        // 发送API请求
                        String requestBody = JsonUtil.toJsonString(requestParams);
                        logger.info("发送语音合成请求, URL: {}, 请求体: {}", apiUrl, requestBody);
                        
                        // 首先检查API是否会返回包含时长信息的JSON响应
                        String jsonResponse = null;
                        byte[] audioData = null;
                        
                        // 尝试首先获取JSON响应（如果API支持）
                        try {
                            // 尝试请求API并获取String类型响应
                            String responseText = OkHttpUtil.post(apiUrl, requestBody);
                            jsonResponse = responseText;
                            
                            // 尝试解析JSON，检查是否包含音频数据的URL或Base64编码
                            JsonNode responseJson = objectMapper.readTree(responseText);
                            
                            // 这里需要根据实际API响应结构进行适配
                            if (responseJson.has("audio_url")) {
                                // 如果API返回了音频URL，则下载音频
                                String audioUrl = responseJson.get("audio_url").asText();
                                audioData = OkHttpUtil.get(audioUrl);
                            } else if (responseJson.has("audio_base64")) {
                                // 如果API返回了Base64编码的音频数据
                                String base64Audio = responseJson.get("audio_base64").asText();
                                audioData = Base64.getDecoder().decode(base64Audio);
                            } else {
                                // 可能API直接返回了二进制音频数据
                                audioData = OkHttpUtil.post(apiUrl, requestBody, headers);
                            }
                        } catch (Exception e) {
                            // 如果解析JSON失败，可能是直接返回的二进制数据
                            logger.info("API未返回JSON格式响应，尝试作为二进制音频处理");
                            audioData = OkHttpUtil.post(apiUrl, requestBody, headers);
                        }
                        
                        if (audioData == null || audioData.length == 0) {
                            throw new RuntimeException("获取音频数据失败");
                        }
                        
                        // 计算音频时长
                        BigDecimal duration;
                        if (jsonResponse != null) {
                            // 如果有JSON响应，尝试从中解析时长
                            duration = parseDurationFromApiResponse(jsonResponse);
                            if (duration.compareTo(BigDecimal.ZERO) == 0) {
                                // 如果JSON中没有时长信息，则计算
                                duration = calculateAudioDuration(audioData);
                            }
                        } else {
                            // 直接计算音频时长
                            duration = calculateAudioDuration(audioData);
                        }
                        
                        // 判断音频是否正常
                        boolean isAudioValid = audioData.length > 1024 && duration.compareTo(BigDecimal.ZERO) > 0;
                        int status = isAudioValid ? 1 : 0; // 状态：1=正常，0=有问题
                        
                        // 上传到MinIO
                        String fileName = UUID.randomUUID().toString() + ".mp3";
                        InputStream inputStream = new ByteArrayInputStream(audioData);
                        
                        String token = tyrfingFileuploadClient.upload(fileName, inputStream, CONTENT_TYPE_MP3, true)
                                .orElseThrow(() -> new TyrfingServiceException(TyrfingErrorCode.FILE_UPLOAD_FAILED, "音频文件上传失败"));
                        String voiceUrl = "https://minio.canghaiguanzhi.com"+tyrfingFileuploadClient.getResourceUrl(token).get();
                        // 创建并保存语音合成记录
                        VoiceSynthesisTb voiceSynthesis = new VoiceSynthesisTb();
                        voiceSynthesis.setPptContentId(pptContent.getId());
                        voiceSynthesis.setVoiceTemplateId(voiceTemplate.getId());
                        voiceSynthesis.setVoiceUrl(voiceUrl);
                        voiceSynthesis.setDuration(duration); // 使用计算的时长
                        voiceSynthesis.setStatus(status); // 使用判断的状态
                        voiceSynthesis.setInsertTime(new Date());
                        voiceSynthesis.setUpdateTime(new Date());
                        
                        logger.info("语音合成完成，文本长度: {}, 音频时长: {}秒, 状态: {}", text.length(), duration, status);
                        
                        // 保存到数据库
                        voiceSynthesisMapper.insert(voiceSynthesis);
                        
                        return voiceSynthesis;
                    } catch (Exception e) {
                        logger.error("语音合成失败: {}", e.getMessage(), e);
                        throw new RuntimeException("语音合成失败", e);
                    }
                }));
            }
            
            // 等待所有异步任务完成
            int successCount = 0;
            for (Future<VoiceSynthesisTb> future : futures) {
                try {
                    VoiceSynthesisTb result = future.get(60, TimeUnit.SECONDS);
                    if (result != null) {
                        successCount++;
                    }
                } catch (Exception e) {
                    logger.error("获取语音合成结果失败: {}", e.getMessage(), e);
                }
            }
            
            logger.info("语音合成完成，总任务: {}，成功: {}", futures.size(), successCount);
            
            return successCount > 0;
        } catch (Exception e) {
            logger.error("视频创建失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
