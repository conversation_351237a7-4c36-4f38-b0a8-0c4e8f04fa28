package com.gungnir.generation.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.paginate.Page;
import com.gungnir.generation.VideoTemplateService;
import com.gungnir.generation.dto.VideoTemplateDTO;
import com.gungnir.generation.mapper.mysql.mapper.VideoTemplateMapper;
import com.gungnir.generation.mapper.mysql.table.VideoTemplateTb;
import com.tyrfing.model.Pagination;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.BeanUtils;

import java.util.*;

/**
 * 视频模板服务实现
 */
@Service
public class VideoTemplateServiceImpl implements VideoTemplateService {

    private final VideoTemplateMapper videoTemplateMapper;

    public VideoTemplateServiceImpl(VideoTemplateMapper videoTemplateMapper) {
        this.videoTemplateMapper = videoTemplateMapper;
    }

    @Override
    public String demo() {
        return "视频模板服务测试成功";
    }

    @Override
    public List<Map<String, Object>> queryAll() {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .orderBy("sort", true)
                .orderBy("insert_time", false);
        List<VideoTemplateTb> templateList = videoTemplateMapper.selectListByQuery(queryWrapper);
        return convertToMapList(templateList);
    }

    @Override
    public Map<String, Object> queryById(String id) {
        VideoTemplateTb template = videoTemplateMapper.selectOneById(id);
        if (template != null) {
            return convertToMap(template);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> queryByStatus(Integer status) {
        List<VideoTemplateTb> templateList = videoTemplateMapper.selectByStatus(status);
        return convertToMapList(templateList);
    }

    @Override
    public List<Map<String, Object>> queryByTitleLike(String keyword) {
        List<VideoTemplateTb> templateList = videoTemplateMapper.selectByTitleLike(keyword);
        return convertToMapList(templateList);
    }

    @Override
    public List<Map<String, Object>> queryByTeacherName(String teacherName) {
        List<VideoTemplateTb> templateList = videoTemplateMapper.selectByTeacherName(teacherName);
        return convertToMapList(templateList);
    }

    @Override
    public List<Map<String, Object>> queryByField(String field) {
        List<VideoTemplateTb> templateList = videoTemplateMapper.selectByField(field);
        return convertToMapList(templateList);
    }

    @Override
    public List<Map<String, Object>> queryRecentAdded(Integer limit) {
        List<VideoTemplateTb> templateList = videoTemplateMapper.selectRecentAdded(limit);
        return convertToMapList(templateList);
    }

    @Override
    public Boolean add(Map<String, Object> params) {
        VideoTemplateTb template = new VideoTemplateTb();
        
        // 设置必填字段
        if (params.get("id") != null) {
            template.setId(Long.parseLong(params.get("id").toString()));
        } else {
            return false; // 必填字段缺失
        }
        
        if (params.get("videoUrl") != null) {
            template.setVideoUrl(params.get("videoUrl").toString());
        } else {
            return false; // 必填字段缺失
        }
        
        // 设置选填字段
        if (params.get("title") != null) {
            template.setTitle(params.get("title").toString());
        }
        
        if (params.get("avatar") != null) {
            template.setAvatar(params.get("avatar").toString());
        }
        
        if (params.get("description") != null) {
            template.setDescription(params.get("description").toString());
        }
        
        if (params.get("teacherName") != null) {
            template.setTeacherName(params.get("teacherName").toString());
        }
        
        if (params.get("field") != null) {
            template.setField(params.get("field").toString());
        }
        
        if (params.get("sort") != null) {
            template.setSort(Integer.valueOf(params.get("sort").toString()));
        }
        
        if (params.get("status") != null) {
            template.setStatus(Integer.valueOf(params.get("status").toString()));
        } else {
            template.setStatus(1); // 默认状态为1
        }
        
        // 设置默认值
        template.setInsertTime(new Date());
        template.setUpdateTime(new Date());
        
        int rows = videoTemplateMapper.insert(template);
        return rows > 0;
    }

    @Override
    public Boolean update(Map<String, Object> params) {
        if (params.get("id") == null) {
            return false;
        }
        
        String id = params.get("id").toString();
        VideoTemplateTb existingTemplate = videoTemplateMapper.selectOneById(id);
        
        if (existingTemplate == null) {
            return false;
        }
        
        VideoTemplateTb template = new VideoTemplateTb();
        template.setId(Long.valueOf(id));
        
        if (params.get("videoUrl") != null) {
            template.setVideoUrl(params.get("videoUrl").toString());
        }
        
        if (params.get("title") != null) {
            template.setTitle(params.get("title").toString());
        }
        
        if (params.get("avatar") != null) {
            template.setAvatar(params.get("avatar").toString());
        }
        
        if (params.get("description") != null) {
            template.setDescription(params.get("description").toString());
        }
        
        if (params.get("teacherName") != null) {
            template.setTeacherName(params.get("teacherName").toString());
        }
        
        if (params.get("field") != null) {
            template.setField(params.get("field").toString());
        }
        
        if (params.get("sort") != null) {
            template.setSort(Integer.valueOf(params.get("sort").toString()));
        }
        
        if (params.get("status") != null) {
            template.setStatus(Integer.valueOf(params.get("status").toString()));
        }
        
        template.setUpdateTime(new Date());
        
        int rows = videoTemplateMapper.update(template);
        return rows > 0;
    }

    @Override
    public Boolean updateStatus(String id, Integer status) {
        int rows = videoTemplateMapper.updateStatus(id, status);
        return rows > 0;
    }

    @Override
    public Boolean delete(String id) {
        int rows = videoTemplateMapper.deleteById(id);
        return rows > 0;
    }

    /**
     * 通用分页查询方法
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    private Pagination<VideoTemplateDTO> executePageQuery(Integer pageNum, Integer pageSize, QueryWrapper queryWrapper) {
        // 执行分页查询
        Page<VideoTemplateTb> resultPage = videoTemplateMapper.paginateByQuery(Page.of(pageNum, pageSize), queryWrapper);
        
        // 转换结果
        List<VideoTemplateDTO> records = new ArrayList<>();
        for (VideoTemplateTb template : resultPage.getRecords()) {
            records.add(convertToDTO(template));
        }
        
        // 创建并返回分页结果
        Pagination<VideoTemplateDTO> pagination = new Pagination<>();
        pagination.setPageNum(pageNum);
        pagination.setPageSize(pageSize);
        pagination.setTotal(resultPage.getTotalRow());
        pagination.setData(records);
        
        return pagination;
    }

    @Override
    public Pagination<VideoTemplateDTO> queryPage(Integer pageNum, Integer pageSize, Map<String, Object> params) {
        // 创建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        // 添加查询条件
        if (params != null) {
            // 标题模糊查询
            if (params.get("title") != null) {
                queryWrapper.like("title", params.get("title").toString());
            }
            
            // 老师姓名查询
            if (params.get("teacherName") != null) {
                queryWrapper.eq("teacher_name", params.get("teacherName").toString());
            }
            
            // 领域查询
            if (params.get("field") != null) {
                queryWrapper.eq("field", params.get("field").toString());
            }
            
            // 状态查询
            if (params.get("status") != null) {
                queryWrapper.eq("status", Integer.valueOf(params.get("status").toString()));
            }
        }
        
        // 默认按排序和插入时间倒序排列
        queryWrapper.orderBy("sort", true).orderBy("insert_time", false);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<VideoTemplateDTO> queryByStatusPage(Integer pageNum, Integer pageSize, Integer status) {
        // 创建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("status", status)
                .orderBy("sort", true)
                .orderBy("insert_time", false);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<VideoTemplateDTO> queryByTitleLikePage(Integer pageNum, Integer pageSize, String keyword) {
        // 创建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .like("title", keyword)
                .orderBy("sort", true)
                .orderBy("insert_time", false);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<VideoTemplateDTO> queryByTeacherNamePage(Integer pageNum, Integer pageSize, String teacherName) {
        // 创建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("teacher_name", teacherName)
                .orderBy("sort", true)
                .orderBy("insert_time", false);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<VideoTemplateDTO> queryByFieldPage(Integer pageNum, Integer pageSize, String field) {
        // 创建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("field", field)
                .orderBy("sort", true)
                .orderBy("insert_time", false);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<VideoTemplateDTO> queryAllPage(Integer pageNum, Integer pageSize) {
        // 创建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .orderBy("sort", true)
                .orderBy("insert_time", false);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    /**
     * 将VideoTemplateTb对象转换为VideoTemplateDTO
     *
     * @param template VideoTemplateTb对象
     * @return VideoTemplateDTO对象
     */
    private VideoTemplateDTO convertToDTO(VideoTemplateTb template) {
        VideoTemplateDTO dto = new VideoTemplateDTO();
        BeanUtils.copyProperties(template, dto);
        return dto;
    }

    /**
     * 将VideoTemplateTb对象转换为Map
     *
     * @param template VideoTemplateTb对象
     * @return Map对象
     */
    private Map<String, Object> convertToMap(VideoTemplateTb template) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", template.getId());
        map.put("videoUrl", template.getVideoUrl());
        map.put("title", template.getTitle());
        map.put("avatar", template.getAvatar());
        map.put("description", template.getDescription());
        map.put("teacherName", template.getTeacherName());
        map.put("field", template.getField());
        map.put("sort", template.getSort());
        map.put("status", template.getStatus());
        map.put("insertTime", template.getInsertTime());
        map.put("updateTime", template.getUpdateTime());
        return map;
    }

    /**
     * 将VideoTemplateTb对象列表转换为Map列表
     *
     * @param templateList VideoTemplateTb对象列表
     * @return Map列表
     */
    private List<Map<String, Object>> convertToMapList(List<VideoTemplateTb> templateList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (templateList != null && !templateList.isEmpty()) {
            for (VideoTemplateTb template : templateList) {
                mapList.add(convertToMap(template));
            }
        }
        return mapList;
    }
} 