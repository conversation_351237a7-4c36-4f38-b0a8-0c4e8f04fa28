package com.gungnir.generation.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateWrapper;
import com.gungnir.generation.PptContentTextService;
import com.gungnir.generation.mapper.mysql.mapper.PptContentTextMapper;
import com.gungnir.generation.mapper.mysql.table.PptContentTextTb;
import org.apache.dubbo.config.annotation.Service;

import java.util.*;

/**
 * PPT讲稿服务实现
 */
@Service
public class PptContentTextServiceImpl implements PptContentTextService {

    private final PptContentTextMapper pptContentTextMapper;

    public PptContentTextServiceImpl(PptContentTextMapper pptContentTextMapper) {
        this.pptContentTextMapper = pptContentTextMapper;
    }

    @Override
    public List<Map<String, Object>> queryAll() {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("delete_status", false)
                .orderBy("sort", true);
        List<PptContentTextTb> contentList = pptContentTextMapper.selectListByQuery(queryWrapper);
        return convertToMapList(contentList);
    }

    @Override
    public Map<String, Object> queryById(Integer id) {
        PptContentTextTb content = pptContentTextMapper.selectOneById(id);
        if (content != null && !content.getDeleteStatus()) {
            return convertToMap(content);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> queryByPptId(Integer pptId) {
        List<PptContentTextTb> contentList = pptContentTextMapper.selectByPptId(pptId);
        return convertToMapList(contentList);
    }

    @Override
    public List<Map<String, Object>> queryByUserId(String userId) {
        List<PptContentTextTb> contentList = pptContentTextMapper.selectByUserId(userId);
        return convertToMapList(contentList);
    }

    @Override
    public List<Map<String, Object>> queryByPptIdAndUserId(Integer pptId, String userId) {
        List<PptContentTextTb> contentList = pptContentTextMapper.selectByPptIdAndUserId(pptId, userId);
        return convertToMapList(contentList);
    }

    @Override
    public List<Map<String, Object>> queryRecentInserted(Integer limit) {
        List<PptContentTextTb> contentList = pptContentTextMapper.selectRecentInserted(limit);
        return convertToMapList(contentList);
    }

    @Override
    public List<Map<String, Object>> queryByTextLike(String keyword) {
        List<PptContentTextTb> contentList = pptContentTextMapper.selectByTextLike(keyword);
        return convertToMapList(contentList);
    }

    @Override
    public Integer add(Map<String, Object> params) {
        PptContentTextTb content = new PptContentTextTb();
        
        // 设置必填字段
        if (params.get("pptId") != null) {
            content.setPptId(Integer.valueOf(params.get("pptId").toString()));
        } else {
            return -1; // 必填字段缺失
        }
        
        if (params.get("userId") != null) {
            content.setUserId(params.get("userId").toString());
        } else {
            return -1; // 必填字段缺失
        }
        
        // 设置选填字段
        if (params.get("url") != null) {
            content.setUrl(params.get("url").toString());
        }
        
        if (params.get("text") != null) {
            content.setText(params.get("text").toString());
        }
        
        if (params.get("editText") != null) {
            content.setEditText(params.get("editText").toString());
        }
        
        if (params.get("polishCount") != null) {
            content.setPolishCount(Integer.valueOf(params.get("polishCount").toString()));
        } else {
            content.setPolishCount(0); // 默认润色次数为0
        }
        
        // 设置默认值
        content.setDeleteStatus(false);
        content.setInsertTime(new Date());
        content.setUpdateTime(new Date());
        
        pptContentTextMapper.insert(content);
        return content.getId();
    }

    @Override
    public Boolean update(Map<String, Object> params) {
        if (params.get("id") == null) {
            return false;
        }
        
        Integer id = Integer.valueOf(params.get("id").toString());
        PptContentTextTb existingContent = pptContentTextMapper.selectOneById(id);
        
        if (existingContent == null || existingContent.getDeleteStatus()) {
            return false;
        }
        
        PptContentTextTb content = new PptContentTextTb();
        content.setId(id);
        
        if (params.get("pptId") != null) {
            content.setPptId(Integer.valueOf(params.get("pptId").toString()));
        }
        
        if (params.get("userId") != null) {
            content.setUserId(params.get("userId").toString());
        }
        
        if (params.get("url") != null) {
            content.setUrl(params.get("url").toString());
        }
        
        if (params.get("text") != null) {
            content.setText(params.get("text").toString());
        }
        
        if (params.get("editText") != null) {
            content.setEditText(params.get("editText").toString());
        }
        
        if (params.get("polishCount") != null) {
            content.setPolishCount(Integer.valueOf(params.get("polishCount").toString()));
        }
        
        content.setUpdateTime(new Date());
        
        int rows = pptContentTextMapper.update(content);
        return rows > 0;
    }

    @Override
    public Boolean incrementPolishCount(Integer id) {
        int rows = pptContentTextMapper.incrementPolishCount(id);
        return rows > 0;
    }

    @Override
    public Boolean updateEditText(Integer id, String text) {
        // 先检查记录是否存在
        PptContentTextTb content = pptContentTextMapper.selectOneById(id);
        if (content == null || content.getDeleteStatus()) {
            return false;
        }
        
        // 创建更新对象
        PptContentTextTb updateEntity = new PptContentTextTb();
        updateEntity.setEditText(text);
        updateEntity.setUpdateTime(new Date());
        updateEntity.setPolishCount(content.getPolishCount() + 1);

        // 使用QueryWrapper作为条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("id", id)
                .eq("delete_status", false);

        int rows = pptContentTextMapper.updateByQuery(updateEntity, queryWrapper);
        return rows > 0;
    }

    @Override
    public Boolean delete(Integer id) {
        PptContentTextTb content = pptContentTextMapper.selectOneById(id);
        if (content == null || content.getDeleteStatus()) {
            return false;
        }
        
        // 创建更新对象
        PptContentTextTb updateEntity = new PptContentTextTb();
        updateEntity.setDeleteStatus(true);
        updateEntity.setDeleteTime(new Date());

        // 使用QueryWrapper作为条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq("id", id);

        int rows = pptContentTextMapper.updateByQuery(updateEntity, queryWrapper);
        return rows > 0;
    }

    @Override
    public Boolean deleteByPptId(Integer pptId) {
        int rows = pptContentTextMapper.deleteByPptId(pptId);
        return rows > 0;
    }

    /**
     * 将PptContentTextTb对象转换为Map
     *
     * @param content PptContentTextTb对象
     * @return Map对象
     */
    private Map<String, Object> convertToMap(PptContentTextTb content) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", content.getId());
        map.put("pptId", content.getPptId());
        map.put("userId", content.getUserId());
        map.put("url", content.getUrl());
        
        // 如果editText有值，则text字段显示editText的内容，否则显示原始text内容
        if (content.getEditText() != null && !content.getEditText().trim().isEmpty()) {
            map.put("text", content.getEditText());
        } else {
            map.put("text", content.getText());
        }
        map.put("polishCount", content.getPolishCount());
        map.put("insertTime", content.getInsertTime());
        map.put("updateTime", content.getUpdateTime());
        return map;
    }

    /**
     * 将PptContentTextTb对象列表转换为Map列表
     *
     * @param contentList PptContentTextTb对象列表
     * @return Map列表
     */
    private List<Map<String, Object>> convertToMapList(List<PptContentTextTb> contentList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (contentList != null && !contentList.isEmpty()) {
            for (PptContentTextTb content : contentList) {
                mapList.add(convertToMap(content));
            }
        }
        return mapList;
    }
} 