package com.gungnir.generation.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gungnir.generation.PptGeneratedHistoryService;
import com.gungnir.generation.dto.PptGeneratedHistoryDTO;
import com.gungnir.generation.mapper.mysql.mapper.PptGeneratedHistoryMapper;
import com.gungnir.generation.mapper.mysql.table.PptGeneratedHistoryTb;
import com.tyrfing.model.Pagination;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.BeanUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PPT生成历史服务实现
 */
@Service
public class PptGeneratedHistoryServiceImpl implements PptGeneratedHistoryService {

    private final PptGeneratedHistoryMapper pptGeneratedHistoryMapper;
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public PptGeneratedHistoryServiceImpl(PptGeneratedHistoryMapper pptGeneratedHistoryMapper) {
        this.pptGeneratedHistoryMapper = pptGeneratedHistoryMapper;
    }

    @Override
    public String demo() {
        return "PPT生成历史服务测试成功";
    }

    @Override
    public List<Map<String, Object>> queryAll() {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = QueryWrapper.create()
                .eq("delete_status", false)
                .orderBy("insert_time", false);
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectListByQuery(queryWrapper);
        return convertToMapList(pptList);
    }

    @Override
    public Map<String, Object> queryById(Integer id) {
        PptGeneratedHistoryTb ppt = pptGeneratedHistoryMapper.selectOneById(id);
        if (ppt != null && !ppt.getDeleteStatus()) {
            return convertToMap(ppt);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> queryByUserId(String userId) {
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectByUserId(userId);
        return convertToMapList(pptList);
    }

    @Override
    public List<Map<String, Object>> queryByDocumentId(String documentId) {
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectByDocumentId(documentId);
        return convertToMapList(pptList);
    }

    @Override
    public List<Map<String, Object>> queryByCourseId(String courseId) {
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectByCourseId(courseId);
        return convertToMapList(pptList);
    }

    @Override
    public List<Map<String, Object>> queryByPptStatus(Integer pptStatus) {
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectByPptStatus(pptStatus);
        return convertToMapList(pptList);
    }

    @Override
    public List<Map<String, Object>> queryRecentGenerated(Integer limit) {
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectRecentGenerated(limit);
        return convertToMapList(pptList);
    }

    @Override
    public List<Map<String, Object>> queryByPptNameLike(String pptName) {
        List<PptGeneratedHistoryTb> pptList = pptGeneratedHistoryMapper.selectByPptNameLike(pptName);
        return convertToMapList(pptList);
    }

    @Override
    public Integer add(Map<String, Object> params) {
        PptGeneratedHistoryTb ppt = new PptGeneratedHistoryTb();
        
        // 设置必填字段
        if (params.get("pptUrl") != null) {
            ppt.setPptUrl(params.get("pptUrl").toString());
        } else {
            return -1; // 必填字段缺失
        }
        
        if (params.get("pptName") != null) {
            ppt.setPptName(params.get("pptName").toString());
        } else {
            return -1; // 必填字段缺失
        }
        
        // 设置选填字段
        if (params.get("pptAvatar") != null) {
            ppt.setPptAvatar(params.get("pptAvatar").toString());
        }
        
        if (params.get("pptSize") != null) {
            ppt.setPptSize(Long.valueOf(params.get("pptSize").toString()));
        }
        
        if (params.get("pptPageCount") != null) {
            ppt.setPptPageCount(Integer.valueOf(params.get("pptPageCount").toString()));
        }
        
        if (params.get("userId") != null) {
            ppt.setUserId(params.get("userId").toString());
        }
        
        if (params.get("documentId") != null) {
            ppt.setDocumentId(params.get("documentId").toString());
        }
        
        if (params.get("courseId") != null) {
            ppt.setCourseId(params.get("courseId").toString());
        }
        
        if (params.get("pptStatus") != null) {
            ppt.setPptStatus(Integer.valueOf(params.get("pptStatus").toString()));
        } else {
            ppt.setPptStatus(1); // 默认状态为正常
        }
        
        if (params.get("aiModelUsed") != null) {
            ppt.setAiModelUsed(params.get("aiModelUsed").toString());
        }
        
        if (params.get("generationParameters") != null) {
            ppt.setGenerationParameters(params.get("generationParameters").toString());
        }
        
        // 设置默认值
        ppt.setDeleteStatus(false);
        ppt.setInsertTime(new Date());
        ppt.setUpdateTime(new Date());
        
        pptGeneratedHistoryMapper.insert(ppt);
        return ppt.getId();
    }

    @Override
    public Boolean update(Map<String, Object> params) {
        if (params.get("id") == null) {
            return false;
        }
        
        Integer id = Integer.valueOf(params.get("id").toString());
        PptGeneratedHistoryTb existingPpt = pptGeneratedHistoryMapper.selectOneById(id);
        
        if (existingPpt == null || existingPpt.getDeleteStatus()) {
            return false;
        }
        
        PptGeneratedHistoryTb ppt = new PptGeneratedHistoryTb();
        ppt.setId(id);
        
        if (params.get("pptUrl") != null) {
            ppt.setPptUrl(params.get("pptUrl").toString());
        }
        
        if (params.get("pptName") != null) {
            ppt.setPptName(params.get("pptName").toString());
        }
        
        if (params.get("pptAvatar") != null) {
            ppt.setPptAvatar(params.get("pptAvatar").toString());
        }
        
        if (params.get("pptSize") != null) {
            ppt.setPptSize(Long.valueOf(params.get("pptSize").toString()));
        }
        
        if (params.get("pptPageCount") != null) {
            ppt.setPptPageCount(Integer.valueOf(params.get("pptPageCount").toString()));
        }
        
        if (params.get("userId") != null) {
            ppt.setUserId(params.get("userId").toString());
        }
        
        if (params.get("documentId") != null) {
            ppt.setDocumentId(params.get("documentId").toString());
        }
        
        if (params.get("courseId") != null) {
            ppt.setCourseId(params.get("courseId").toString());
        }
        
        if (params.get("pptStatus") != null) {
            ppt.setPptStatus(Integer.valueOf(params.get("pptStatus").toString()));
        }
        
        if (params.get("aiModelUsed") != null) {
            ppt.setAiModelUsed(params.get("aiModelUsed").toString());
        }
        
        if (params.get("generationParameters") != null) {
            ppt.setGenerationParameters(params.get("generationParameters").toString());
        }
        
        ppt.setUpdateTime(new Date());
        
        int rows = pptGeneratedHistoryMapper.update(ppt);
        return rows > 0;
    }

    @Override
    public Boolean delete(Integer id) {
        PptGeneratedHistoryTb ppt = pptGeneratedHistoryMapper.selectOneById(id);
        if (ppt == null || ppt.getDeleteStatus()) {
            return false;
        }

        UpdateWrapper<PptGeneratedHistoryTb> updateWrapper = UpdateWrapper.of(PptGeneratedHistoryTb.class)
                .eq("id", id)
                .set("delete_status", true)
                .set("delete_time", new Date());

        int rows = pptGeneratedHistoryMapper.updateByQuery(null, updateWrapper);
        return rows > 0;
    }

    /**
     * 执行分页查询
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param queryWrapper 查询条件
     * @return 分页查询结果
     */
    private Pagination<PptGeneratedHistoryDTO> executePageQuery(Integer pageNum, Integer pageSize, QueryWrapper<PptGeneratedHistoryTb> queryWrapper) {
        // 默认排序
        if (queryWrapper.getExpression().getOrderBy().isEmpty()) {
            queryWrapper.orderByDesc("insert_time");
        }

        // 创建分页对象
        Page<PptGeneratedHistoryTb> page = new Page<>(pageNum, pageSize);
        // 执行分页查询
        Page<PptGeneratedHistoryTb> resultPage = pptGeneratedHistoryMapper.selectPage(page, queryWrapper);
        // 转换为DTO对象
        List<PptGeneratedHistoryDTO> records = resultPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        // 构建返回的分页对象
        Pagination<PptGeneratedHistoryDTO> pagination = new Pagination<>();
        pagination.setPageNum(pageNum);
        pagination.setPageSize(pageSize);
        pagination.setTotal(resultPage.getTotal());
        pagination.setData(records);

        return pagination;
    }

    /**
     * 将PptGeneratedHistoryTb对象转换为PptGeneratedHistoryDTO对象
     * @param ppt 数据库实体对象
     * @return DTO对象
     */
    private PptGeneratedHistoryDTO convertToDTO(PptGeneratedHistoryTb ppt) {
        if (ppt == null) {
            return null;
        }
        
        PptGeneratedHistoryDTO dto = new PptGeneratedHistoryDTO();
        BeanUtils.copyProperties(ppt, dto);
        return dto;
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryPage(Integer pageNum, Integer pageSize, Map<String, Object> params) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false);
        
        // 设置查询条件
        if (params != null && !params.isEmpty()) {
            if (params.get("userId") != null) {
                queryWrapper.eq("user_id", params.get("userId").toString());
            }
            
            if (params.get("pptName") != null) {
                queryWrapper.like("ppt_name", params.get("pptName").toString());
            }
            
            if (params.get("documentId") != null) {
                queryWrapper.eq("document_id", params.get("documentId").toString());
            }
            
            if (params.get("courseId") != null) {
                queryWrapper.eq("course_id", params.get("courseId").toString());
            }
            
            if (params.get("pptStatus") != null) {
                queryWrapper.eq("ppt_status", Integer.valueOf(params.get("pptStatus").toString()));
            }
        }
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryByUserIdPage(Integer pageNum, Integer pageSize, String userId) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false)
                .eq("user_id", userId);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryByDocumentIdPage(Integer pageNum, Integer pageSize, String documentId) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false)
                .eq("document_id", documentId);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryByPptNameLikePage(Integer pageNum, Integer pageSize, String pptName) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false)
                .like("ppt_name", pptName);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryByPptStatusPage(Integer pageNum, Integer pageSize, Integer pptStatus) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false)
                .eq("ppt_status", pptStatus);
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryByCourseIdPage(Integer pageNum, Integer pageSize, String courseId) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false)
                .eq("course_id", courseId);

        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    @Override
    public Pagination<PptGeneratedHistoryDTO> queryAllPage(Integer pageNum, Integer pageSize) {
        QueryWrapper<PptGeneratedHistoryTb> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_status", false)
                .orderByDesc("insert_time");
        
        return executePageQuery(pageNum, pageSize, queryWrapper);
    }

    /**
     * 将PptGeneratedHistoryTb对象转换为Map
     *
     * @param ppt PptGeneratedHistoryTb对象
     * @return Map对象
     */
    private Map<String, Object> convertToMap(PptGeneratedHistoryTb ppt) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", ppt.getId());
        map.put("pptUrl", ppt.getPptUrl());
        map.put("pptName", ppt.getPptName());
        map.put("pptAvatar", ppt.getPptAvatar());
        map.put("pptSize", ppt.getPptSize());
        map.put("pptPageCount", ppt.getPptPageCount());
        map.put("userId", ppt.getUserId());
        map.put("documentId", ppt.getDocumentId());
        map.put("courseId", ppt.getCourseId());
        map.put("insertTime", ppt.getInsertTime());
        map.put("pptStatus", ppt.getPptStatus());
        map.put("aiModelUsed", ppt.getAiModelUsed());
        map.put("generationParameters", ppt.getGenerationParameters());
        return map;
    }

    /**
     * 将PptGeneratedHistoryTb对象列表转换为Map列表
     *
     * @param pptList PptGeneratedHistoryTb对象列表
     * @return Map列表
     */
    private List<Map<String, Object>> convertToMapList(List<PptGeneratedHistoryTb> pptList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (pptList != null && !pptList.isEmpty()) {
            for (PptGeneratedHistoryTb ppt : pptList) {
                mapList.add(convertToMap(ppt));
            }
        }
        return mapList;
    }
} 