package com.gungnir.generation.impl;

import com.gungnir.generation.VoiceSynthesisService;
import com.gungnir.generation.mapper.mysql.mapper.VoiceSynthesisMapper;
import com.gungnir.generation.mapper.mysql.table.VoiceSynthesisTb;
import org.apache.dubbo.config.annotation.Service;

import java.util.*;

/** 语音合成服务实现
 * <AUTHOR> 2025年05月15日09:43
 * 邮件: <EMAIL>
 * 作者: AlanQuain
 */
@Service
public class VoiceSynthesisServiceImpl implements VoiceSynthesisService {

    private final VoiceSynthesisMapper voiceSynthesisMapper;

    public VoiceSynthesisServiceImpl(VoiceSynthesisMapper voiceSynthesisMapper) {
        this.voiceSynthesisMapper = voiceSynthesisMapper;
    }

    @Override
    public String demo() {
        System.out.println("queryAll");
        List<VoiceSynthesisTb> voiceSynthesisList = voiceSynthesisMapper.selectAll();
        return voiceSynthesisList.toString();
//        return "123123";
    }

    @Override
    public List<Map<String, Object>> queryAll() {
        List<VoiceSynthesisTb> voiceSynthesisList = voiceSynthesisMapper.selectAll();
        return convertToMapList(voiceSynthesisList);
    }

    @Override
    public Map<String, Object> queryById(String synthesisId) {
        VoiceSynthesisTb synthesis = voiceSynthesisMapper.selectById(synthesisId);
        return synthesis != null ? convertToMap(synthesis) : null;
    }

    @Override
    public List<Map<String, Object>> queryBySegmentId(String segmentId) {
        List<VoiceSynthesisTb> synthesisList = voiceSynthesisMapper.selectBySegmentId(segmentId);
        return convertToMapList(synthesisList);
    }

    @Override
    public List<Map<String, Object>> queryByActorId(String actorId) {
        List<VoiceSynthesisTb> synthesisList = voiceSynthesisMapper.selectByActorId(actorId);
        return convertToMapList(synthesisList);
    }

    @Override
    public List<Map<String, Object>> queryByStatus(String status) {
        List<VoiceSynthesisTb> synthesisList = voiceSynthesisMapper.selectByStatus(status);
        return convertToMapList(synthesisList);
    }

    @Override
    public Map<String, Object> queryBySegmentAndActor(String segmentId, String actorId) {
        VoiceSynthesisTb synthesis = voiceSynthesisMapper.selectBySegmentAndActor(segmentId, actorId);
        return synthesis != null ? convertToMap(synthesis) : null;
    }


    @Override
    public List<Map<String, Object>> queryRecentSynthesis(Integer limit) {
        List<VoiceSynthesisTb> synthesisList = voiceSynthesisMapper.selectRecentSynthesis(limit);
        return convertToMapList(synthesisList);
    }

    /**
     * 将VoiceSynthesisTb对象转换为Map
     *
     * @param synthesis VoiceSynthesisTb对象
     * @return Map对象
     */
    private Map<String, Object> convertToMap(VoiceSynthesisTb synthesis) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", synthesis.getId());
        map.put("pptContentId", synthesis.getPptContentId());
        map.put("voiceTemplateId", synthesis.getVoiceTemplateId());
        map.put("voiceUrl", synthesis.getVoiceUrl());
        map.put("duration", synthesis.getDuration());
        map.put("status", synthesis.getStatus());
        map.put("insertTime", synthesis.getInsertTime());
        map.put("updateTime", synthesis.getUpdateTime());
        return map;
    }

    /**
     * 将VoiceSynthesisTb对象列表转换为Map列表
     *
     * @param synthesisList VoiceSynthesisTb对象列表
     * @return Map列表
     */
    private List<Map<String, Object>> convertToMapList(List<VoiceSynthesisTb> synthesisList) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (synthesisList != null && !synthesisList.isEmpty()) {
            for (VoiceSynthesisTb synthesis : synthesisList) {
                mapList.add(convertToMap(synthesis));
            }
        }
        return mapList;
    }
} 