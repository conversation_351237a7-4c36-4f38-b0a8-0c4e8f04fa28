spring:
  config:
    activate:
      on-profile: default
  lifecycle:
    timeout-per-shutdown-phase: 30s
  application:
    name: dt
  redis:
    host: 127.0.0.1
    port: 6379
  data:
    mongodb:
      uri: mongodb://${MONGO_URI:localhost}:27017/gungnir
#    mongodb:
#   这里写 没用的
#      #      这里配置的才是真的 在服务器上 master
#      uri: mongodb://${DB_MONGO_USERNAME:admin}:${DB_MONGO_PASSWORD:DTVvfygCrhhdCD3W}@${DB_MONGO_HOST:*************}:${MONGO_PORT:31002}/${DB_MONGO_DB:digital_teacher}?authSource=admin
  datasource:
    dynamic:
      primary: tyrfing
      datasource:
        tyrfing:
          driver-class-name: com.mysql.jdbc.Driver
#          url: jdbc:mysql://${MYSQL_HOST:*************}:31001/dt?useUnicode=true&useSSL=false&characterEncoding=utf8
          url: jdbc:mysql://${MYSQL_HOST:*************}:31001/digital_teacher?useUnicode=true&useSSL=false&characterEncoding=utf8
#          url: jdbc:mysql://${MYSQL_HOST:*************}:31001/qa?useUnicode=true&useSSL=false&characterEncoding=utf8
          username: root
          password: xLo2UyaB2ArTMjT3
      strict: false
      druid:
        # Druid连接池配置
        initialSize: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat
        useGlobalDataSourceStat: true
#        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
#        init-connection-sqls:
  elasticsearch:
    rest:
      uris: ${ES_HOST:localhost}:9200
      connection-timeout: 1s
      read-timeout: 30s
mybatis-flex:
  mapper-locations: classpath:mapper/*.xml
dubbo:
  application:
    name: ${spring.application.name}
  protocol:
    host: localhost
    name: dubbo
    port: 10050
  registry:
    protocol: zookeeper
    client: curator
    simplified: true
#    address: localhost:2181
    address: 127.0.0.1:2181
  provider:
    filter: tyrfing_primary_filter
    retries: 0
    timeout: 20000
  consumer:
    filter: tyrfing_primary_filter
    timeout: 20000
    check: false
  scan:
    base-packages: com.gungnir
server:
  port: 8080
  servlet:
    context-path: /dapi/v2/gz-generation
    encoding:
      force: true
      enabled: true
      charset: UTF-8
  shutdown: graceful
  tomcat:
    uri-encoding: UTF-8
tyrfing:
  hostname: localhost
  auth:
    all-service-required: false
    sso: false
  config:
    kubernetes:
      namespace: gungnir
  data:
    mongodb:
#      dds:
#        default-database: gungnir
#        default-database: tyrfing
#          this.primary = "tyrfing";
      primary: gungnir
      dds:
        strict: false
      uri:
##        gungnir: mongodb://admin:<EMAIL>:37081/search
##        gungnir: ******************************************************
#        gungnir: mongodb://localhost:27017/digital_teacher
        gungnir: mongodb://${DB_MONGO_USERNAME:admin}:${DB_MONGO_PASSWORD:DTVvfygCrhhdCD3W}@${DB_MONGO_HOST:*************}:${MONGO_PORT:31002}/${DB_MONGO_DB:digital_teacher}?authSource=admin
    mysql:
      dds:
#        database: dt
        database: digital_teacher
  fileupload:
    host: ${MINIO_HOST:*************}
    port: ${MINIO_PORT:32302}
    protocol: ${MINIO_PROTOCOL:http}
    bucket-name: ${BUCKET_NAME:gungnir-file}
    access-key: ${MINIO_ACCESS_KEY:admin}
    secret-key: ${MINIO_SECRET_KEY:gungnir_minio_1234_pwd}
  faq-algorithm:
    question-url: ${TYRFING_FAQ_ALGORITHM_QUESTION_URL:http://*************:8010}
    document-url: ${TYRFING_FAQ_ALGORITHM_DOCUMENT_URL:http://*************:8010}
    file-service-url: ${TYRFING_FAQ_ALGORITHM_FILE_SERVICE_URL:https://intelligentqa.canghaiguanzhi.com}
    server-node-num: ${TYRFING_FAQ_ALGORITHM_SERVER_NODE_NUM:8}
  open-buddy:
    uri: ${TYRFING_OPEN_BUDDY_URI:http://*************:8124}
    #token: ${TYRFING_OPEN_BUDDY_TOKEN:uTOKpaknpht410apkd}
    api-key: ${TYRFING_OPEN_BUDDY_APIKEY:a2KDHanCdahtlAKHlkd10}
    model: ${TYRFING_OPEN_BUDDY_MODEL:34b}
    temperature: ${TYRFING_OPEN_BUDDY_TEMPERATURE:0.3}
    max-new-tokens: ${TYRFING_OPEN_BUDDY_MAX_NEW_TOKENS:8000}
    max-tokens: ${TYRFING_OPEN_BUDDY_MAX_TOKENS:1000}
    translate:
      uri: ${TYRFING_OPEN_BUDDY_TRANSLATE_URI:http://*************:10343}
      token: ${TYRFING_OPEN_BUDDY_TRANSLATE_TOKEN:20230808-englearn-fnqpzkbnqpcngv}
      model: ${TYRFING_OPEN_BUDDY_TRANSLATE_TRANSLATE_MODEL:7b}
      temperature: ${TYRFING_OPEN_BUDDY_TRANSLATE_TEMPERATURE:0.05}
      max-new-tokens: ${TYRFING_OPEN_BUDDY_TRANSLATE_MAX_NEW_TOKENS:1000}
  rag:
    uri: ${TYRFING_RAG_URI:http://122.226.162.86:8301}
  api-auth:
    app-code: ${TYRFING_API_AUTH_APP_CODE:70}
    access-token: ${TYRFING_API_AUTH_ACCESS_TOKEN:e1194be95eb332ed39139bddfd42af10}
  text-check:
    access-key: ${TYRFING_TEXT_CHECK_ACCESS_KEY:LTAI5tCVZY6mjYH5faUZhX2b}
    access-key-secret: ${TYRFING_TEXT_CHECK_ACCESS_KEY_SECRET:******************************}
  oss-resource:
    pdf-uri-prefix: ${TYRFING_OSS_RESOURCE_PDF_URI_PREFIX:https://minio.canghaiguanzhi.com/gungnir-static/}
  job-switch:
    doc-analyze: ${TYRFING_JOB_SWITCH_DOC_ANALYZE:0}
logging:
  level:
    #java.sql: DEBUG
    #com.gungnir.dt.mysql.mapper: DEBUG
    #org.mybatis.spring: DEBUG
    org:
      zalando:
        logbook: trace
logbook:
  exclude:
    - /**/actuator/health/*
    - /**/api/admin/**

