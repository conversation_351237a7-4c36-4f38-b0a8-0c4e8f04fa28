spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  application:
    name: dt
  redis:
    host: ${REDIS_HOST:localhost}
    port: 6379
  datasource:
    dynamic:
      primary: tyrfing
      datasource:
        tyrfing:
          driver-class-name: com.mysql.jdbc.Driver
          #          url: jdbc:mysql://${MYSQL_HOST:localhost}:3306/dt?useUnicode=true&useSSL=false&characterEncoding=utf8
          url: jdbc:mysql://${MYSQL_HOST:localhost}:3306/digital_teacher?useUnicode=true&useSSL=false&characterEncoding=utf8
          username: root
          #          password: ${MYSQL_PWD:123456}
          password: ${MYSQL_PWD:xLo2UyaB2ArTMjT3}
      strict: false
      druid:
        # Druid连接池配置
        initialSize: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat
        useGlobalDataSourceStat: true

  elasticsearch:
    rest:
      uris: ${ES_HOST:localhost}:9200
      connection-timeout: 1s
      read-timeout: 30s
mybatis-flex:
  mapper-locations: classpath:mapper/*.xml
dubbo:
  application:
    name: ${spring.application.name}
  protocol:
    host: ${HOSTNAME}
    name: dubbo
    port: 10050
  registry:
    protocol: zookeeper
    client: curator
    simplified: true
    address: ${REGISTRY}
  provider:
    threads: ${DUBBO_THREADS:1024}
    filter: tyrfing_primary_filter
    retries: 0
    timeout: 20000
  consumer:
    threads: ${DUBBO_THREADS:1024}
    filter: tyrfing_primary_filter
    timeout: 20000
    check: false
  scan:
    base-packages: com.gungnir
server:
  servlet:
    context-path: /dapi/v2/gz-generation
    encoding:
      force: true
      enabled: true
      charset: UTF-8
  port: ${TOMCAT_PORT:8080}
  #  port: ${TOMCAT_PORT:8082}
  shutdown: graceful
  tomcat:
    uri-encoding: UTF-8
tyrfing:
  hostname: ${HOSTNAME}
  auth:
    all-service-required: true
  config:
    kubernetes:
      namespace: gungnir
  data:
    mongodb:
      #      primary: digital_teacher
      dds:
        ##        default-database: gungnir
        default-database: digital_teacher
    #        databases:
    #          - digital_teacher
    #        strict: true
    ##        strict: false
    #      uri:
    ###        gungnir: mongodb://admin:<EMAIL>:37081/search
    ###        gungnir: ******************************************************
    ###        gungnir: mongodb://admin:<EMAIL>:31002/digital_teacher
    #        digital_teacher: ********************************************************************
    mysql:
      dds:
        #        database: dt
        database: digital_teacher
  tracing:
    collector-host: ${JAEGER_COLLECTOR_HOST}
  fileupload:
    host: ${MINIO_HOST}
    port: ${MINIO_PORT:9000}
    protocol: ${MINIO_PROTOCOL:http}
    bucket-name: ${BUCKET_NAME:gungnir}
    access-key: ${MINIO_ACCESS_KEY:admin}
    secret-key: ${MINIO_SECRET_KEY:12345678}
  faq-algorithm:
    question-url: ${TYRFING_FAQ_ALGORITHM_QUESTION_URL:http://**************:8000}
    document-url: ${TYRFING_FAQ_ALGORITHM_DOCUMENT_URL:http://**************:8000}
    file-service-url: ${TYRFING_FAQ_ALGORITHM_FILE_SERVICE_URL:http://intelligentqa.canghaiguanzhi.com}
    server-node-num: ${TYRFING_FAQ_ALGORITHM_SERVER_NODE_NUM:8}
  open-buddy:
    uri: ${TYRFING_OPEN_BUDDY_URI:http://**************:8124}
    #uri: ${TYRFING_OPEN_BUDDY_URI:http://*************:10343}
    #token: ${TYRFING_OPEN_BUDDY_TOKEN:uTOKpaknpht410apkd}
    #api-key: ${TYRFING_OPEN_BUDDY_APIKEY:aKDHanCdahtlAKHlkd10}
    api-key: ${TYRFING_OPEN_BUDDY_APIKEY:a2KDHanCdahtlAKHlkd10}
    model: ${TYRFING_OPEN_BUDDY_MODEL:34b}
    temperature: ${TYRFING_OPEN_BUDDY_TEMPERATURE:0.3}
    max-new-tokens: ${TYRFING_OPEN_BUDDY_MAX_NEW_TOKENS:8000}
    max-tokens: ${TYRFING_OPEN_BUDDY_MAX_TOKENS:1000}
    translate:
      uri: ${TYRFING_OPEN_BUDDY_TRANSLATE_URI:http://*************:11344}
      token: ${TYRFING_OPEN_BUDDY_TRANSLATE_TOKEN:20230808-englearn-fnqpzkbnqpcngv}
      model: ${TYRFING_OPEN_BUDDY_TRANSLATE_TRANSLATE_MODEL:7b}
      temperature: ${TYRFING_OPEN_BUDDY_TRANSLATE_TEMPERATURE:0.05}
      max-new-tokens: ${TYRFING_OPEN_BUDDY_TRANSLATE_MAX_NEW_TOKENS:1000}
  rag:
    uri: ${TYRFING_RAG_URI:http://**************:8301}
  api-auth:
    app-code: ${TYRFING_API_AUTH_APP_CODE:70}
    access-token: ${TYRFING_API_AUTH_ACCESS_TOKEN:e1194be95eb332ed39139bddfd42af10}
  text-check:
    access-key: ${TYRFING_TEXT_CHECK_ACCESS_KEY:LTAI5tCVZY6mjYH5faUZhX2b}
    access-key-secret: ${TYRFING_TEXT_CHECK_ACCESS_KEY_SECRET:******************************}
  oss-resource:
    pdf-uri-prefix: ${TYRFING_OSS_RESOURCE_PDF_URI_PREFIX:https://minio.canghaiguanzhi.com/gungnir-static/}
  job-switch:
    doc-analyze: ${TYRFING_JOB_SWITCH_DOC_ANALYZE:1}
logging:
  level:
    org:
      zalando:
        logbook: trace
logbook:
  exclude:
    - /**/actuator/health/*
    - /**/api/admin/**

