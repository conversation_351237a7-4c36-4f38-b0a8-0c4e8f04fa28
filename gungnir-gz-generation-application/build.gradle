plugins {
    id 'java'
}

group 'com.gungnir'
version '1.0.0-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.2'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.2'

//    implementation "com.tyrfing:tyrfing-spring-boot-mysql-starter:$tyrfingVersion"

    implementation("com.tyrfing:tyrfing-spring-boot-mysql-starter:$tyrfingVersion") {
        exclude group: 'com.baomidou', module: 'mybatis-plus-boot-starter'
//        com.baomidou:mybatis-plus-boot-starter:3.1.1
    }

//    implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.0'
    implementation 'com.mybatis-flex:mybatis-flex-spring-boot-starter:1.8.3'
    // 添加核心依赖
    implementation 'com.mybatis-flex:mybatis-flex-core:1.8.3'
    // 添加注解处理器
    annotationProcessor 'com.mybatis-flex:mybatis-flex-processor:1.8.3'

    implementation "com.tyrfing:tyrfing-spring-boot-redis-starter:$tyrfingVersion"
    implementation "com.tyrfing:tyrfing-spring-boot-config-kubernetes-starter:$tyrfingVersion"
    implementation "com.tyrfing:tyrfing-spring-boot-excel-starter:$tyrfingVersion"
    implementation "com.tyrfing:tyrfing-spring-boot-auth-starter:$tyrfingVersion"
    implementation "com.tyrfing:tyrfing-spring-boot-security-starter:$tyrfingVersion"

//    任务要用
    implementation "com.gungnir:gungnir-assist-library:2.0.1-SNAPSHOT"
    implementation "com.gungnir:gungnir-office-library:1.0.1"

    //文件服务
    implementation "com.tyrfing:tyrfing-fileupload-library:1.6.0"
    implementation 'cn.hutool:hutool-all:5.8.22'
    //阿里云文本审核
//    implementation 'com.aliyun:green20220302:1.0.3'
    //fastjson
    implementation 'com.alibaba:fastjson:1.2.41'

    implementation 'org.apache.pdfbox:pdfbox:2.0.24'

//    implementation "org.elasticsearch:elasticsearch:7.9.3"
//    implementation("org.springframework.boot:spring-boot-starter-data-elasticsearch:2.4.5") {
//        exclude group: 'org.elasticsearch.plugin', module: 'transport-netty4-client'
//    }

    implementation "com.gungnir:gungnir-account-library:1.16.1"

    implementation "com.tyrfing:tyrfing-spring-boot-mongo-starter:$tyrfingVersion"
    //Mongo数据库
//    implementation "com.tyrfing:tyrfing-spring-boot-mongo-starter:$tyrfingVersion"/**/

//    implementation 'com.alibaba:druid:1.2.22'  // 使用具体的版本号
//    implementation 'com.alibaba:druid-spring-boot-starter:1.2.22'  // 使用具体的版本号

    implementation 'com.fasterxml.jackson.core:jackson-databind:2.11.2'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.11.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.11.2'


}

test {
    useJUnitPlatform()
}